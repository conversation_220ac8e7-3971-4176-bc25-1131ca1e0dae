#!/usr/bin/env python3
"""
Quick test to verify enhanced error handling is working
"""

import sys
import os
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from telegram_integration.telegram_api import TelegramMessaging

def test_enhanced_error():
    """Test the enhanced error handling directly"""
    
    print("🧪 Quick Enhanced Error Test")
    print("=" * 30)
    
    # Initialize TelegramMessaging (the enhanced class)
    telegram = TelegramMessaging()
    
    print(f"✅ Using TelegramMessaging class")
    print(f"🤖 Bot connected: {telegram.connection_status == 'connected'}")
    
    # Test with the exact username from your error
    chat_id = "@Kinkyamiee_stores"
    message = "hey dear"
    
    print(f"\n📨 Testing: {chat_id}")
    print(f"💬 Message: {message}")
    
    # Call send_message and capture the result
    result = telegram.send_message(chat_id, message)
    
    print(f"\n📊 Enhanced Response:")
    print(json.dumps(result, indent=2))
    
    # Check if enhanced fields are present
    enhanced_fields = ['error', 'reason', 'solutions', 'chat_id', 'telegram_error', 'unipile_error']
    present_fields = [field for field in enhanced_fields if field in result]
    missing_fields = [field for field in enhanced_fields if field not in result]
    
    print(f"\n🔍 Enhanced Fields Analysis:")
    print(f"   Present: {present_fields}")
    print(f"   Missing: {missing_fields}")
    
    # Show specific guidance
    if result.get('solutions'):
        print(f"\n💡 Solutions provided:")
        for i, solution in enumerate(result['solutions'], 1):
            print(f"   {i}. {solution}")
    
    print(f"\n🎯 Summary:")
    if 'solutions' in result and result.get('reason'):
        print("   ✅ Enhanced error handling is working!")
        print("   ✅ Specific guidance is provided")
        print("   ✅ Solutions are included")
    else:
        print("   ❌ Enhanced error handling not working")
        print("   ❌ Basic error response returned")
    
    return result

if __name__ == "__main__":
    test_enhanced_error()
