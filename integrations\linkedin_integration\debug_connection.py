"""
Debug LinkedIn Connection Issues
Simple script to diagnose the 401 Unauthorized error
"""

import sys
import os
import json
import requests
from datetime import datetime

# Add the parent directory to the path so we can import linkedin_api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_unipile_connection():
    """Test Unipile API connection directly"""
    print("🔍 Testing Unipile API Connection")
    print("=" * 40)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    try:
        # Test basic API connection
        print("1. Testing basic API connection...")
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Unipile API connection successful")
            
            # Check for LinkedIn accounts
            accounts = data.get("items", [])
            linkedin_accounts = [acc for acc in accounts if acc.get("type") == "LINKEDIN"]
            
            print(f"   Total accounts: {len(accounts)}")
            print(f"   LinkedIn accounts: {len(linkedin_accounts)}")
            
            if linkedin_accounts:
                for i, acc in enumerate(linkedin_accounts):
                    print(f"   LinkedIn Account {i+1}:")
                    print(f"     ID: {acc.get('id')}")
                    print(f"     Name: {acc.get('name', 'N/A')}")
                    print(f"     Status: {acc.get('status', 'N/A')}")
                return linkedin_accounts[0].get('id')
            else:
                print("   ❌ No LinkedIn accounts found")
                return None
        else:
            print(f"   ❌ Unipile API failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return None
            
    except Exception as e:
        print(f"   ❌ Unipile connection error: {e}")
        return None

def test_linkedin_api_fallback():
    """Test LinkedIn API fallback configuration"""
    print("\n🔍 Testing LinkedIn API Fallback")
    print("=" * 40)
    
    try:
        # Load config
        config_path = "config.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        linkedin_config = config.get("linkedin_api", {})
        
        print("LinkedIn API Configuration:")
        print(f"   Client ID: {linkedin_config.get('client_id')}")
        print(f"   Access Token: {'SET' if linkedin_config.get('access_token') and linkedin_config.get('access_token') != 'YOUR_LINKEDIN_ACCESS_TOKEN' else 'NOT SET'}")
        print(f"   Person ID: {linkedin_config.get('person_id')}")
        
        # Check if credentials are placeholder values
        if linkedin_config.get('client_id') == 'YOUR_LINKEDIN_CLIENT_ID':
            print("   ❌ LinkedIn API credentials are placeholder values")
            print("   ❌ This is why you're getting 401 Unauthorized errors")
            return False
        else:
            print("   ✅ LinkedIn API credentials appear to be configured")
            return True
            
    except Exception as e:
        print(f"   ❌ Error checking LinkedIn API config: {e}")
        return False

def test_connection_request_flow():
    """Test the actual connection request flow"""
    print("\n🔍 Testing Connection Request Flow")
    print("=" * 40)
    
    try:
        from linkedin_api import LinkedInMessaging
        
        # Initialize LinkedIn messaging
        linkedin = LinkedInMessaging(use_unipile=True)
        
        # Get connection status
        status = linkedin.get_connection_status()
        
        print("Connection Status:")
        print(f"   Unipile Available: {status.get('unipile', {}).get('available', False)}")
        print(f"   Unipile Connected: {status.get('unipile', {}).get('connected', False)}")
        print(f"   LinkedIn API Available: {status.get('linkedin_api', {}).get('available', False)}")
        print(f"   LinkedIn API Connected: {status.get('linkedin_api', {}).get('connected', False)}")
        
        # Test with a dummy connection request to see the error flow
        print("\n   Testing dummy connection request...")
        result = linkedin.send_connection_message("test-user", "Test message")
        
        if result.get("success"):
            print("   ✅ Connection request would succeed")
        else:
            error = result.get("error", "Unknown error")
            print(f"   ❌ Connection request failed: {error}")
            
            # Analyze the error
            if "401" in error or "Unauthorized" in error:
                print("   🔍 This is the 401 Unauthorized error you're seeing")
                print("   💡 Root cause: LinkedIn API fallback has invalid credentials")
            elif "Unipile" in error:
                print("   🔍 This is a Unipile API error")
            else:
                print("   🔍 This is a different type of error")
        
        return result
        
    except Exception as e:
        print(f"   ❌ Error testing connection flow: {e}")
        return None

def main():
    """Main diagnostic function"""
    print("LinkedIn Connection Diagnostic Tool")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test 1: Unipile connection
    account_id = test_unipile_connection()
    
    # Test 2: LinkedIn API fallback
    linkedin_api_ok = test_linkedin_api_fallback()
    
    # Test 3: Connection request flow
    test_connection_request_flow()
    
    # Summary and recommendations
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSIS SUMMARY")
    print("=" * 50)
    
    if account_id:
        print("✅ Unipile API is working and has LinkedIn account connected")
        print(f"   Account ID: {account_id}")
    else:
        print("❌ Unipile API connection failed")
    
    if not linkedin_api_ok:
        print("❌ LinkedIn API fallback has placeholder credentials")
        print("   This is causing the 401 Unauthorized error")
    
    print("\n💡 RECOMMENDED FIXES:")
    
    if account_id and not linkedin_api_ok:
        print("1. 🎯 PRIMARY ISSUE: LinkedIn API fallback credentials are not configured")
        print("   - The system tries Unipile first (which works)")
        print("   - When Unipile fails for specific operations, it falls back to LinkedIn API")
        print("   - LinkedIn API has placeholder credentials, causing 401 Unauthorized")
        print("\n   SOLUTIONS:")
        print("   A) Configure real LinkedIn API credentials in config.json")
        print("   B) OR disable LinkedIn API fallback and fix Unipile issues")
        print("   C) OR improve error handling to not fallback on certain errors")
    
    elif not account_id:
        print("1. Fix Unipile API connection first")
        print("2. Then configure LinkedIn API fallback credentials")
    
    print("\n📋 NEXT STEPS:")
    print("1. Check what specific Unipile error is causing fallback")
    print("2. Either fix Unipile issue or configure LinkedIn API credentials")
    print("3. Test with a real LinkedIn profile identifier")

if __name__ == "__main__":
    main()
