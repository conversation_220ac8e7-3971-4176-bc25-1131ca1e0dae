#!/usr/bin/env python3
"""
LinkedIn Configuration Setup Script
Helps users configure their LinkedIn API credentials properly
"""

import json
import os
import sys
from datetime import datetime

def load_config():
    """Load the current configuration"""
    # Try different possible locations for config.json
    possible_paths = [
        "config.json",
        "integrations/linkedin_integration/config.json",
        os.path.join(os.path.dirname(__file__), "config.json")
    ]

    config_path = None
    for path in possible_paths:
        if os.path.exists(path):
            config_path = path
            break

    if not config_path:
        print(f"❌ Config file not found in any of these locations: {possible_paths}")
        return None
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Config file not found: {config_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config file: {e}")
        return None

def save_config(config):
    """Save the configuration"""
    config_path = "config.json"
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ Configuration saved to {config_path}")
        return True
    except Exception as e:
        print(f"❌ Error saving config: {e}")
        return False

def check_config_status(config):
    """Check the status of all configuration fields"""
    print("\n📋 Configuration Status Check")
    print("=" * 50)
    
    # Unipile configuration
    print("\n🔧 Unipile Configuration (Primary Method):")
    unipile_config = config.get("unipile", {})
    
    api_key = unipile_config.get("api_key", "")
    account_id = unipile_config.get("account_id", "")
    
    print(f"  API Key: {'✅ Set' if api_key else '❌ Empty'}")
    print(f"  Account ID: {'✅ Set' if account_id else '⚠️  Empty (will auto-detect)'}")
    print(f"  Enabled: {'✅ Yes' if unipile_config.get('enabled', False) else '❌ No'}")
    
    # LinkedIn API configuration
    print("\n🔗 LinkedIn API Configuration (Fallback Method):")
    linkedin_config = config.get("linkedin_api", {})
    
    fields = {
        "client_id": "Client ID",
        "client_secret": "Client Secret", 
        "access_token": "Access Token",
        "refresh_token": "Refresh Token",
        "person_id": "Person ID"
    }
    
    linkedin_status = {}
    for field, label in fields.items():
        value = linkedin_config.get(field, "")
        is_set = value and value != f"YOUR_LINKEDIN_{field.upper()}"
        linkedin_status[field] = is_set
        status = "✅ Set" if is_set else "❌ Empty"
        print(f"  {label}: {status}")
    
    # Overall status
    print("\n📊 Overall Status:")
    unipile_ready = bool(api_key)
    linkedin_ready = all(linkedin_status.values())
    
    print(f"  Unipile Ready: {'✅ Yes' if unipile_ready else '❌ No'}")
    print(f"  LinkedIn API Ready: {'✅ Yes' if linkedin_ready else '❌ No'}")
    
    if unipile_ready:
        print("  🎉 Primary method (Unipile) is configured!")
    elif linkedin_ready:
        print("  ⚠️  Only fallback method (LinkedIn API) is configured")
    else:
        print("  ❌ No messaging method is fully configured")
    
    return unipile_ready, linkedin_ready

def setup_unipile_config(config):
    """Setup Unipile configuration"""
    print("\n🔧 Unipile Configuration Setup")
    print("=" * 40)
    
    print("Unipile is the recommended primary method for LinkedIn messaging.")
    print("It's easier to set up and more reliable than the native LinkedIn API.")
    
    unipile_config = config.setdefault("unipile", {})
    
    # API Key
    current_key = unipile_config.get("api_key", "")
    print(f"\nCurrent API Key: {'Set' if current_key else 'Not set'}")
    
    new_key = input("Enter your Unipile API Key (or press Enter to keep current): ").strip()
    if new_key:
        unipile_config["api_key"] = new_key
        config["unipile_api_key"] = new_key  # Also set the top-level key for compatibility
        print("✅ API Key updated")
    
    # Account ID
    current_account = unipile_config.get("account_id", "")
    print(f"\nCurrent Account ID: {current_account if current_account else 'Not set (will auto-detect)'}")
    
    new_account = input("Enter LinkedIn Account ID (or press Enter to auto-detect): ").strip()
    if new_account:
        unipile_config["account_id"] = new_account
        print("✅ Account ID updated")
    else:
        print("ℹ️  Account ID will be auto-detected when first used")
    
    # Enable Unipile
    unipile_config["enabled"] = True
    config["use_unipile"] = True
    
    print("✅ Unipile configuration updated!")

def setup_linkedin_api_config(config):
    """Setup LinkedIn API configuration"""
    print("\n🔗 LinkedIn API Configuration Setup")
    print("=" * 45)
    
    print("LinkedIn API is used as a fallback when Unipile is unavailable.")
    print("This requires a LinkedIn Developer App and OAuth setup.")
    
    linkedin_config = config.setdefault("linkedin_api", {})
    
    fields = {
        "client_id": "Client ID (from LinkedIn Developer App)",
        "client_secret": "Client Secret (from LinkedIn Developer App)",
        "access_token": "Access Token (from OAuth flow)",
        "refresh_token": "Refresh Token (from OAuth flow)",
        "person_id": "Person ID (your LinkedIn member ID)"
    }
    
    print("\n📋 LinkedIn API Setup Instructions:")
    print("1. Go to https://developer.linkedin.com/")
    print("2. Create a new app or use existing app")
    print("3. Add 'w_member_social' and 'r_liteprofile' permissions")
    print("4. Use OAuth 2.0 flow to get tokens")
    print("5. Get person_id from /v2/people/~ endpoint")
    
    for field, description in fields.items():
        current_value = linkedin_config.get(field, "")
        is_placeholder = current_value.startswith("YOUR_LINKEDIN_")
        
        print(f"\n{description}:")
        print(f"Current: {'Not set' if not current_value or is_placeholder else 'Set'}")
        
        new_value = input(f"Enter {field} (or press Enter to skip): ").strip()
        if new_value:
            linkedin_config[field] = new_value
            print(f"✅ {field} updated")
    
    print("✅ LinkedIn API configuration updated!")

def test_configuration():
    """Test the current configuration"""
    print("\n🧪 Testing Configuration")
    print("=" * 30)
    
    try:
        # Import and test the LinkedIn messaging
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        linkedin = LinkedInMessaging(use_unipile=True)
        
        # Test connection status
        status = linkedin.get_connection_status()
        
        print("Connection Test Results:")
        unipile_connected = status.get('unipile', {}).get('connected', False)
        linkedin_connected = status.get('linkedin_api', {}).get('connected', False)
        
        print(f"  Unipile: {'✅ Connected' if unipile_connected else '❌ Not connected'}")
        print(f"  LinkedIn API: {'✅ Connected' if linkedin_connected else '❌ Not connected'}")
        
        if unipile_connected:
            accounts = status.get('unipile', {}).get('accounts', [])
            print(f"  Connected LinkedIn accounts: {len(accounts)}")
            for acc in accounts:
                print(f"    - {acc.get('id')} ({acc.get('type')})")
        
        if not unipile_connected and not linkedin_connected:
            print("\n⚠️  No working connection found. Please check your configuration.")
        else:
            print("\n✅ Configuration test completed successfully!")
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")

def main():
    """Main configuration setup function"""
    print("LinkedIn Messaging Configuration Setup")
    print("=" * 50)
    print(f"Started at: {datetime.now().isoformat()}")
    
    # Load current config
    config = load_config()
    if not config:
        return
    
    # Check current status
    unipile_ready, linkedin_ready = check_config_status(config)
    
    # Setup menu
    while True:
        print("\n🔧 Configuration Options:")
        print("1. Setup Unipile Configuration (Recommended)")
        print("2. Setup LinkedIn API Configuration (Fallback)")
        print("3. Test Current Configuration")
        print("4. Show Current Status")
        print("5. Save and Exit")
        print("6. Exit without Saving")
        
        choice = input("\nSelect an option (1-6): ").strip()
        
        if choice == "1":
            setup_unipile_config(config)
        elif choice == "2":
            setup_linkedin_api_config(config)
        elif choice == "3":
            test_configuration()
        elif choice == "4":
            check_config_status(config)
        elif choice == "5":
            if save_config(config):
                print("✅ Configuration saved successfully!")
                print("\n🎉 Setup complete! You can now use LinkedIn messaging.")
            break
        elif choice == "6":
            print("❌ Exiting without saving changes.")
            break
        else:
            print("❌ Invalid option. Please select 1-6.")

if __name__ == "__main__":
    main()
