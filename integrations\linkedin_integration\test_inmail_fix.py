#!/usr/bin/env python3
"""
Test the InMail identifier resolution fix
Demonstrates the issue and shows improved error handling
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def test_inmail_identifier_resolution():
    """Test the enhanced InMail identifier resolution"""
    print("📧 Testing InMail Identifier Resolution Fix")
    print("=" * 50)
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Get the current account ID
    account_id = linkedin._get_linkedin_account_id()
    print(f"Your LinkedIn Account ID: {account_id}")
    
    if not account_id:
        print("❌ No LinkedIn account ID found. Please check your Unipile connection.")
        return
    
    # Test 1: Try to send InMail to self (should fail)
    print("\n1. Testing InMail to self (should fail)...")
    result = linkedin.send_inmail(account_id, "Test Subject", "Hello myself!")
    
    if result.get("error"):
        print("✅ Self-InMail properly blocked!")
        print(f"   Error: {result['error']}")
        print(f"   Suggestion: {result.get('suggestion', 'N/A')}")
    else:
        print("❌ Self-InMail was not blocked (this is a bug)")
    
    # Test 2: Test with the problematic identifier from the logs
    print("\n2. Testing InMail with public identifier (mayowa-ade)...")
    test_recipient = "mayowa-ade"  # The one that failed in the logs
    test_subject = "Professional Opportunity"
    test_message = "Hello! I'd like to discuss a professional opportunity with you."
    
    print(f"   Recipient: {test_recipient}")
    print(f"   Subject: {test_subject}")
    print(f"   Message: {test_message[:50]}...")
    
    # This should now handle the identifier resolution and provide better error messages
    result = linkedin.send_inmail(test_recipient, test_subject, test_message)
    
    print(f"\n   InMail result:")
    if result.get("success"):
        print("   ✅ InMail sent successfully!")
        print(f"   Method: {result.get('method')}")
        print(f"   Endpoint used: {result.get('result', {}).get('endpoint_used', 'Unknown')}")
        if result.get('resolved_recipient_id') != test_recipient:
            print(f"   Resolved ID: {result.get('resolved_recipient_id')}")
    else:
        print(f"   ❌ InMail failed: {result.get('error')}")
        
        # Check the type of error and provide guidance
        error_msg = result.get('error', '')
        if 'Cannot send InMail to yourself' in error_msg:
            print("   → This is a self-InMail prevention (expected)")
        elif 'Invalid recipient identifier' in error_msg:
            print("   → This is an identifier resolution error")
            print(f"   → Suggestion: {result.get('suggestion', 'N/A')}")
        elif 'HTTP 422' in error_msg or 'Unprocessable Entity' in error_msg:
            print("   → This is an API format error - the fix should handle this better")
        else:
            print("   → This is an unexpected error")

def test_different_identifier_formats():
    """Test InMail with different identifier formats"""
    print("\n📝 Testing Different Identifier Formats")
    print("-" * 45)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    account_id = linkedin._get_linkedin_account_id()
    
    test_cases = [
        ("john-doe", "Public identifier format"),
        ("jane-smith-123", "Public identifier with numbers"),
        ("ACoAAABCDEF123456789", "Provider ID format"),
        ("mayowa-ade", "The problematic identifier from logs"),
        ("", "Empty identifier (should fail validation)"),
        ("<EMAIL>", "Invalid format (should fail validation)"),
        (account_id, "Self-identifier (should fail self-check)")
    ]
    
    for test_id, description in test_cases:
        print(f"\n   Testing: '{test_id}' - {description}")
        result = linkedin.send_inmail(
            test_id, 
            "Test Subject", 
            "This is a test InMail message."
        )
        
        if result.get("success"):
            print(f"   ✅ Would send successfully")
            if result.get('resolved_recipient_id') != test_id:
                print(f"   → Resolved to: {result.get('resolved_recipient_id')}")
        else:
            error = result.get('error', 'Unknown error')
            print(f"   ❌ Failed: {error[:80]}...")
            
            # Show suggestion if available
            suggestion = result.get('suggestion')
            if suggestion:
                print(f"   💡 Suggestion: {suggestion[:80]}...")

def demonstrate_error_improvements():
    """Demonstrate the improved error messages for InMail"""
    print("\n🔍 InMail Error Message Improvements")
    print("-" * 40)
    
    print("Before fix:")
    print("  - Generic HTTP 422: Unprocessable Entity")
    print("  - No guidance on what went wrong")
    print("  - No suggestions for resolution")
    
    print("\nAfter fix:")
    print("  - Clear explanation of the issue")
    print("  - Specific suggestions for resolution")
    print("  - Multiple endpoint attempts")
    print("  - Identifier resolution attempts")
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test with the problematic identifier
    result = linkedin.send_inmail("mayowa-ade", "Test", "Test message")
    
    print(f"\n📋 Enhanced Error Response Example:")
    print(f"   Error: {result.get('error', 'N/A')}")
    print(f"   Suggestion: {result.get('suggestion', 'N/A')}")
    print(f"   Original Error: {result.get('original_error', 'N/A')}")
    print(f"   Attempted Recipient: {result.get('attempted_recipient', 'N/A')}")

def show_proper_inmail_usage():
    """Show examples of proper InMail usage"""
    print("\n📚 Proper InMail Usage Examples")
    print("-" * 35)
    
    print("✅ CORRECT - Using valid LinkedIn identifiers:")
    print("""
# Example 1: Using public identifier from LinkedIn URL
# URL: https://linkedin.com/in/john-doe
linkedin.send_inmail(
    recipient_id="john-doe",
    subject="Professional Opportunity",
    message="Hi John, I have an exciting opportunity to discuss..."
)

# Example 2: Using provider ID (if you have it)
linkedin.send_inmail(
    recipient_id="ACoAAABCDEF123456789",
    subject="Business Collaboration",
    message="Hello! I'd like to explore potential collaboration..."
)
""")
    
    print("❌ INCORRECT - Common mistakes:")
    print("""
# Using your own account ID
linkedin.send_inmail("0gqlbGFeQ-uxqXfvVFYCrg", "Subject", "Message")

# Using invalid formats
linkedin.send_inmail("<EMAIL>", "Subject", "Message")

# Empty or missing fields
linkedin.send_inmail("", "", "")
""")
    
    print("💡 Tips for successful InMails:")
    print("""
1. Use LinkedIn public identifiers from profile URLs
2. Verify the profile exists and accepts InMails
3. Keep subjects under 200 characters
4. Keep messages under 1900 characters
5. Personalize your messages for better response rates
""")

def main():
    """Main test function"""
    print("LinkedIn InMail Identifier Resolution Fix Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    try:
        test_inmail_identifier_resolution()
        test_different_identifier_formats()
        demonstrate_error_improvements()
        show_proper_inmail_usage()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        
        print("\n🎯 Summary of InMail fixes:")
        print("1. ✅ Enhanced identifier resolution with multiple endpoint attempts")
        print("2. ✅ Self-InMail prevention with clear error messages")
        print("3. ✅ Better error messages for HTTP 422 errors")
        print("4. ✅ Multiple InMail format attempts for better compatibility")
        print("5. ✅ Helpful suggestions for identifier resolution issues")
        
        print("\n📋 Next steps:")
        print("1. Use valid LinkedIn public identifiers from profile URLs")
        print("2. Verify profiles exist and accept InMails")
        print("3. Test with real LinkedIn profiles for actual messaging")
        print("4. Monitor success rates and adjust approach if needed")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\nTest completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
