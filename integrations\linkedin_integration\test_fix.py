"""
Test the LinkedIn 401 Unauthorized Fix
Simple script to verify the fix works correctly
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import linkedin_api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_linkedin_fix():
    """Test the LinkedIn 401 fix"""
    print("🔧 Testing LinkedIn 401 Unauthorized Fix")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        # Initialize LinkedIn messaging
        print("\n1. Initializing LinkedIn messaging...")
        linkedin = LinkedInMessaging(use_unipile=True)
        print("   ✅ LinkedIn messaging initialized")
        
        # Check configuration
        print(f"   Use Unipile: {linkedin.use_unipile}")
        print(f"   Use LinkedIn API Fallback: {linkedin.use_linkedin_api_fallback}")
        
        # Test connection status
        print("\n2. Checking connection status...")
        status = linkedin.get_connection_status()
        
        unipile_connected = status.get('unipile', {}).get('connected', False)
        linkedin_api_available = status.get('linkedin_api', {}).get('available', False)
        
        print(f"   Unipile connected: {'✅' if unipile_connected else '❌'}")
        print(f"   LinkedIn API available: {'✅' if linkedin_api_available else '❌'}")
        
        # Test connection request (this should NOT give 401 error anymore)
        print("\n3. Testing connection request...")
        print("   Testing with invalid recipient (should fail gracefully)...")
        
        result = linkedin.send_connection_message("test-invalid-user", "Test message")
        
        if result.get("success"):
            print("   ✅ Connection request succeeded (unexpected)")
        else:
            error = result.get("error", "Unknown error")
            print(f"   ❌ Connection request failed (expected): {error[:100]}...")
            
            # Check if we get the 401 error
            if "401" in error or "Unauthorized" in error:
                print("   🚨 STILL GETTING 401 ERROR - Fix didn't work")
                return False
            elif "unipile_failed" in result.get("method", ""):
                print("   ✅ Getting proper Unipile failure message - Fix worked!")
                return True
            elif "fallback is disabled" in error:
                print("   ✅ Getting proper fallback disabled message - Fix worked!")
                return True
            else:
                print("   ℹ️  Getting different error - may be expected")
                return True
        
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inmail_fix():
    """Test the InMail 401 fix"""
    print("\n4. Testing InMail fix...")
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        linkedin = LinkedInMessaging(use_unipile=True)
        
        # Test InMail (this should NOT give 401 error anymore)
        result = linkedin.send_inmail("test-invalid-user", "Test Subject", "Test message")
        
        if result.get("success"):
            print("   ✅ InMail succeeded (unexpected)")
        else:
            error = result.get("error", "Unknown error")
            print(f"   ❌ InMail failed (expected): {error[:100]}...")
            
            # Check if we get the 401 error
            if "401" in error or "Unauthorized" in error:
                print("   🚨 STILL GETTING 401 ERROR - Fix didn't work")
                return False
            elif "unipile_failed" in result.get("method", ""):
                print("   ✅ Getting proper Unipile failure message - Fix worked!")
                return True
            elif "fallback is disabled" in error:
                print("   ✅ Getting proper fallback disabled message - Fix worked!")
                return True
            else:
                print("   ℹ️  Getting different error - may be expected")
                return True
                
    except Exception as e:
        print(f"   ❌ Error during InMail test: {e}")
        return False

def main():
    """Main test function"""
    print("LinkedIn 401 Unauthorized Fix Test")
    print("=" * 50)
    
    # Test connection request fix
    connection_fix_works = test_linkedin_fix()
    
    # Test InMail fix
    inmail_fix_works = test_inmail_fix()
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    if connection_fix_works and inmail_fix_works:
        print("✅ ALL TESTS PASSED - 401 Unauthorized fix is working!")
        print("\n💡 What was fixed:")
        print("1. ✅ LinkedIn API fallback is now disabled by default")
        print("2. ✅ Clear error messages when Unipile fails")
        print("3. ✅ No more 401 Unauthorized errors from invalid LinkedIn API credentials")
        print("4. ✅ Configuration option to control fallback behavior")
        
        print("\n📋 Next steps:")
        print("1. Use valid LinkedIn profile identifiers for real testing")
        print("2. Check Unipile account connection if messages still fail")
        print("3. Optionally configure LinkedIn API credentials if you want fallback")
        
    else:
        print("❌ SOME TESTS FAILED - Fix may need adjustment")
        print(f"   Connection request fix: {'✅' if connection_fix_works else '❌'}")
        print(f"   InMail fix: {'✅' if inmail_fix_works else '❌'}")
    
    print("\n🔧 Configuration:")
    print("   use_linkedin_api_fallback: false (in config.json)")
    print("   This prevents 401 errors from invalid LinkedIn API credentials")

if __name__ == "__main__":
    main()
