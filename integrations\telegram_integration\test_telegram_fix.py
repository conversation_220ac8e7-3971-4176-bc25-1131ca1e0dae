#!/usr/bin/env python3
"""
Test the enhanced Telegram error handling and validation
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from telegram_integration.telegram_api import TelegramMessaging

def test_chat_id_validation():
    """Test the chat ID validation functionality"""
    print("🔍 Testing Chat ID Validation")
    print("=" * 40)
    
    # Initialize Telegram messaging
    telegram = TelegramMessaging()
    
    test_cases = [
        ("123456789", "Valid numeric user ID"),
        ("-1001234567890", "Valid group ID"),
        ("@kinkyamiee", "Username format (the problematic one)"),
        ("@john_doe", "Valid username format"),
        ("@user123", "Valid username with numbers"),
        ("kinkyamiee", "Username without @ (should be normalized)"),
        ("@ab", "Too short username (should fail)"),
        ("@user@invalid", "Invalid username with @"),
        ("", "Empty chat ID (should fail)"),
        ("invalid", "Invalid format")
    ]
    
    for chat_id, description in test_cases:
        print(f"\n   Testing: '{chat_id}' - {description}")
        
        # Test normalization
        normalized = telegram._normalize_chat_id(chat_id)
        print(f"   Normalized: '{normalized}'")
        
        # Test validation
        validation = telegram._validate_chat_id(normalized)
        if validation["valid"]:
            print(f"   ✅ Valid: {validation['message']}")
            if "warning" in validation:
                print(f"   ⚠️  Warning: {validation['warning']}")
        else:
            print(f"   ❌ Invalid: {validation['message']}")
            print(f"   💡 Suggestion: {validation['suggestion']}")

def test_enhanced_error_messages():
    """Test the enhanced error message functionality"""
    print("\n🔧 Testing Enhanced Error Messages")
    print("=" * 40)
    
    telegram = TelegramMessaging()
    
    # Test different error scenarios
    error_scenarios = [
        ("chat not found", "@kinkyamiee", "Chat not found error"),
        ("forbidden: bot was blocked by the user", "@someuser", "User blocked bot"),
        ("unauthorized", "@anyuser", "Invalid bot token"),
        ("unknown error", "@testuser", "Generic error")
    ]
    
    for error_msg, chat_id, description in error_scenarios:
        print(f"\n   Testing: {description}")
        print(f"   Error: {error_msg}")
        print(f"   Chat ID: {chat_id}")
        
        enhanced_error = telegram._enhance_telegram_error(error_msg, chat_id, "Unipile not connected")
        
        print(f"   Enhanced Error: {enhanced_error.get('error')}")
        print(f"   Reason: {enhanced_error.get('reason', 'N/A')}")
        
        solutions = enhanced_error.get('solutions', [])
        if solutions:
            print("   Solutions:")
            for i, solution in enumerate(solutions, 1):
                print(f"     {solution}")

def test_actual_message_sending():
    """Test actual message sending with the problematic chat ID"""
    print("\n📨 Testing Actual Message Sending")
    print("=" * 40)
    
    telegram = TelegramMessaging()
    
    # Test the exact same call that was failing
    print("Testing message to @kinkyamiee (the problematic case)...")
    result = telegram.send_message("@kinkyamiee", "hey dear, how are you doing?")
    
    print(f"\n📋 Enhanced Result:")
    print(f"   Success: {result.get('success', False)}")
    
    if result.get('error'):
        print(f"   Error: {result['error']}")
        print(f"   Reason: {result.get('reason', 'N/A')}")
        
        solutions = result.get('solutions', [])
        if solutions:
            print("   Solutions:")
            for i, solution in enumerate(solutions, 1):
                print(f"     {solution}")
    else:
        print(f"   Message ID: {result.get('message_id')}")
        print(f"   Method: {result.get('method')}")

def test_different_chat_formats():
    """Test different chat ID formats"""
    print("\n📝 Testing Different Chat Formats")
    print("=" * 40)
    
    telegram = TelegramMessaging()
    
    test_chats = [
        "kinkyamiee",  # Without @
        "@kinkyamiee",  # With @
        "123456789",   # Numeric user ID
        "-1001234567890"  # Group ID
    ]
    
    for chat_id in test_chats:
        print(f"\n   Testing chat ID: '{chat_id}'")
        result = telegram.send_message(chat_id, "Test message")
        
        if result.get('success'):
            print("   ✅ Would send successfully")
        else:
            error = result.get('error', 'Unknown error')
            print(f"   ❌ Failed: {error[:80]}...")
            
            # Show specific guidance
            reason = result.get('reason')
            if reason:
                print(f"   📝 Reason: {reason}")

def show_telegram_setup_guidance():
    """Show guidance for proper Telegram setup"""
    print("\n📚 Telegram Setup Guidance")
    print("=" * 35)
    
    print("🤖 For Bot API Method:")
    print("1. Create a bot with @BotFather on Telegram")
    print("2. Get the bot token")
    print("3. Users must start a conversation with your bot first")
    print("4. Use numeric user IDs when possible")
    
    print("\n🔗 For Unipile Method (Recommended):")
    print("1. Connect your personal Telegram account via Unipile")
    print("2. Can message any contact in your Telegram")
    print("3. More reliable for direct messaging")
    print("4. No need for users to start conversations first")
    
    print("\n💡 Common Issues & Solutions:")
    print("• 'Chat not found' → User hasn't started conversation with bot")
    print("• 'Forbidden' → User blocked the bot or bot lacks permissions")
    print("• 'Unauthorized' → Invalid bot token")
    print("• Use Unipile for better reliability")

def main():
    """Main test function"""
    print("Telegram Integration Error Handling Fix Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    try:
        test_chat_id_validation()
        test_enhanced_error_messages()
        test_actual_message_sending()
        test_different_chat_formats()
        show_telegram_setup_guidance()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        
        print("\n🎯 Summary of fixes:")
        print("1. ✅ Enhanced chat ID validation with normalization")
        print("2. ✅ Specific error messages for 'chat not found' errors")
        print("3. ✅ Clear guidance on how to resolve common issues")
        print("4. ✅ Better handling of username formats")
        print("5. ✅ Helpful suggestions for each error type")
        
        print("\n📋 Next steps:")
        print("1. For @kinkyamiee: Ask them to start a conversation with your bot first")
        print("2. Or use Unipile integration for better reliability")
        print("3. Consider using numeric user IDs when available")
        print("4. Test with users who have started conversations with your bot")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\nTest completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
