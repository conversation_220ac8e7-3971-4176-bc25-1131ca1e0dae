#!/usr/bin/env python3
"""
Telegram First Message Helper
Solutions for initiating conversations with users when bots can't send first messages
"""

import sys
import os
from typing import Dict, List, Union
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from telegram_integration.telegram_api import TelegramMessaging

class TelegramFirstMessageHelper:
    """Helper class for handling Telegram's first message limitation"""
    
    def __init__(self, bot_username: str = "@AtomGroup123_bot"):
        self.bot_username = bot_username.replace('@', '')
        self.telegram = TelegramMessaging()
        
    def generate_start_link(self, start_parameter: str = None) -> str:
        """Generate a deep link that opens the bot for users"""
        base_url = f"https://t.me/{self.bot_username}"
        
        if start_parameter:
            return f"{base_url}?start={start_parameter}"
        else:
            return base_url
    
    def generate_personalized_start_link(self, username: str) -> Dict:
        """Generate a personalized start link for a specific user"""
        clean_username = username.replace('@', '')
        start_param = f"{clean_username}_welcome"
        link = self.generate_start_link(start_param)
        
        return {
            "username": username,
            "start_link": link,
            "start_parameter": start_param,
            "instructions": [
                f"Send this link to {username}:",
                f"Link: {link}",
                "",
                "When they click it:",
                "1. Telegram opens with your bot",
                "2. They see a 'Start' button", 
                "3. They click 'Start'",
                "4. Bot receives the start command",
                "5. Bot can now message them anytime!"
            ]
        }
    
    def create_invitation_message(self, username: str, purpose: str = "receive updates") -> Dict:
        """Create an invitation message to send via other channels"""
        link_info = self.generate_personalized_start_link(username)
        
        invitation_text = f"""
🤖 Hi {username}!

To {purpose} from our Telegram bot, please start a conversation:

1️⃣ Click this link: {link_info['start_link']}
2️⃣ Click the "Start" button in Telegram
3️⃣ You'll immediately receive a welcome message!

Or manually:
📱 Open Telegram → Search "@{self.bot_username}" → Click "Start"

Thanks! 🙏
        """.strip()
        
        return {
            "username": username,
            "invitation_text": invitation_text,
            "start_link": link_info['start_link'],
            "methods": [
                "Send via email",
                "Send via SMS", 
                "Send via other social media",
                "Share in person",
                "Post in groups/channels"
            ]
        }
    
    def try_send_via_unipile(self, username: str, message: str) -> Dict:
        """Try to send message via Unipile (personal account)"""
        print(f"🔄 Attempting to send via Unipile to {username}...")
        
        result = self.telegram.send_message(username, message)
        
        if result.get('success'):
            return {
                "success": True,
                "method": "unipile",
                "message": f"Message sent successfully via Unipile to {username}",
                "result": result
            }
        else:
            return {
                "success": False,
                "method": "unipile",
                "error": result.get('error'),
                "fallback_needed": True
            }
    
    def handle_user_wants_to_message(self, username: str, message: str) -> Dict:
        """Complete workflow for messaging a user who hasn't started conversation"""
        
        print(f"🎯 Attempting to message {username}...")
        
        # Step 1: Try Unipile first (personal account)
        unipile_result = self.try_send_via_unipile(username, message)
        
        if unipile_result['success']:
            return unipile_result
        
        # Step 2: Generate invitation for bot conversation
        invitation = self.create_invitation_message(username, "receive this message")
        
        return {
            "success": False,
            "method": "invitation_required",
            "message": f"Cannot send direct message to {username}. User must start conversation first.",
            "unipile_error": unipile_result.get('error'),
            "solution": {
                "type": "send_invitation",
                "invitation_text": invitation['invitation_text'],
                "start_link": invitation['start_link'],
                "methods": invitation['methods']
            },
            "original_message": message,
            "username": username
        }
    
    def create_group_mention_strategy(self, group_id: str, username: str, message: str) -> Dict:
        """Create a strategy to reach user via group mention"""
        
        group_message = f"""
@{username.replace('@', '')} 👋

{message}

To continue this conversation privately, please start a chat with our bot:
🤖 @{self.bot_username}

Just search for the bot and click "Start"!
        """.strip()
        
        return {
            "strategy": "group_mention",
            "group_id": group_id,
            "group_message": group_message,
            "instructions": [
                f"Send this message in group {group_id}:",
                f"Message: {group_message}",
                "",
                "This will:",
                f"1. Notify {username} in the group",
                "2. Provide instructions to start bot conversation",
                "3. Allow private messaging after they start"
            ]
        }
    
    def bulk_invitation_generator(self, usernames: List[str], purpose: str = "receive updates") -> Dict:
        """Generate invitations for multiple users"""
        
        invitations = []
        
        for username in usernames:
            invitation = self.create_invitation_message(username, purpose)
            invitations.append(invitation)
        
        # Create a summary message for bulk sending
        bulk_message = f"""
🤖 Telegram Bot Invitation

Hi everyone! To {purpose} from our Telegram bot @{self.bot_username}:

1️⃣ Open Telegram
2️⃣ Search for: @{self.bot_username}  
3️⃣ Click "Start"
4️⃣ You'll receive a welcome message!

Or use these personalized links:
        """.strip()
        
        for inv in invitations:
            bulk_message += f"\n• {inv['username']}: {inv['start_link']}"
        
        bulk_message += "\n\nThanks! 🙏"
        
        return {
            "total_users": len(usernames),
            "individual_invitations": invitations,
            "bulk_message": bulk_message,
            "distribution_methods": [
                "Email newsletter",
                "SMS campaign", 
                "Social media post",
                "Website announcement",
                "Group/channel message"
            ]
        }

def demo_usage():
    """Demonstrate how to use the helper"""
    print("🤖 Telegram First Message Helper Demo")
    print("=" * 45)
    
    helper = TelegramFirstMessageHelper("@AtomGroup123_bot")
    
    # Example 1: Try to message @kinkyamiee
    print("\n📨 Example 1: Messaging @kinkyamiee")
    print("-" * 30)
    
    result = helper.handle_user_wants_to_message(
        "@kinkyamiee", 
        "hey dear, how are you doing?"
    )
    
    if result['success']:
        print("✅ Message sent successfully!")
        print(f"Method: {result['method']}")
    else:
        print("❌ Direct message failed - invitation required")
        print(f"Reason: {result['message']}")
        print("\n💡 Solution:")
        print(result['solution']['invitation_text'])
    
    # Example 2: Generate start link
    print("\n🔗 Example 2: Generate Start Link")
    print("-" * 30)
    
    link_info = helper.generate_personalized_start_link("@kinkyamiee")
    print(f"Start Link: {link_info['start_link']}")
    print("Instructions:")
    for instruction in link_info['instructions']:
        print(f"  {instruction}")
    
    # Example 3: Bulk invitations
    print("\n📢 Example 3: Bulk Invitations")
    print("-" * 30)
    
    users = ["@kinkyamiee", "@user2", "@user3"]
    bulk_result = helper.bulk_invitation_generator(users, "receive marketing updates")
    
    print(f"Generated invitations for {bulk_result['total_users']} users")
    print("\nBulk message:")
    print(bulk_result['bulk_message'])

if __name__ == "__main__":
    demo_usage()
