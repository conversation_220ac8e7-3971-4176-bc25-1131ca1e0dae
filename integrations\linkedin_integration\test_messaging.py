#!/usr/bin/env python3
"""
LinkedIn Messaging Test Script
Tests the enhanced LinkedIn messaging functionality
"""

import sys
import os
import json
from datetime import datetime

# Add the parent directory to the path so we can import the LinkedIn API
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging, validate_linkedin_id, sanitize_message

def test_basic_functionality():
    """Test basic LinkedIn messaging functionality"""
    print("🚀 Starting LinkedIn Messaging Tests")
    print("=" * 50)
    
    # Initialize the LinkedIn messaging client
    try:
        linkedin = LinkedInMessaging(use_unipile=True)
        print("✅ LinkedInMessaging initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize LinkedInMessaging: {e}")
        return False
    
    # Test configuration loading
    print(f"📁 Config path: {linkedin.config_path}")
    print(f"🔧 Unipile enabled: {linkedin.use_unipile}")
    print(f"🔑 Unipile client available: {linkedin.unipile_client is not None}")
    
    return linkedin

def test_validation_functions():
    """Test input validation functions"""
    print("\n🔍 Testing Validation Functions")
    print("-" * 30)
    
    # Test LinkedIn ID validation
    test_ids = [
        ("ACoAAABCDEF123456789", True),
        ("john-doe", True),
        ("jane-smith-123", True),
        ("", False),
        ("a", False),
        ("<EMAIL>", False),
        ("123", False)
    ]
    
    print("LinkedIn ID Validation:")
    for test_id, expected in test_ids:
        result = validate_linkedin_id(test_id)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{test_id}': {result}")
    
    # Test message sanitization
    print("\nMessage Sanitization:")
    test_messages = [
        "Normal message",
        "Message   with    extra    spaces",
        "A" * 100,  # Long message
        "   Leading and trailing spaces   "
    ]
    
    for msg in test_messages:
        sanitized = sanitize_message(msg, max_length=50)
        print(f"  Original: '{msg[:30]}...'")
        print(f"  Sanitized: '{sanitized}'")
        print()

def test_messaging_methods(linkedin):
    """Test messaging methods with validation"""
    print("\n📨 Testing Messaging Methods")
    print("-" * 30)
    
    # Test InMail with invalid inputs
    print("Testing InMail validation:")
    
    # Empty inputs
    result = linkedin.send_inmail("", "", "")
    print(f"  Empty inputs: {result.get('error', 'No error')}")
    
    # Invalid recipient ID
    result = linkedin.send_inmail("invalid-id", "Test Subject", "Test message")
    print(f"  Invalid ID: {result.get('error', 'No error')}")
    
    # Too long subject
    long_subject = "A" * 250
    result = linkedin.send_inmail("test-id", long_subject, "Test message")
    print(f"  Long subject: {result.get('error', 'No error')}")
    
    # Too long message
    long_message = "B" * 2000
    result = linkedin.send_inmail("test-id", "Test Subject", long_message)
    print(f"  Long message: {result.get('error', 'No error')}")
    
    # Test connection message validation
    print("\nTesting Connection Message validation:")
    
    # Empty inputs
    result = linkedin.send_connection_message("", "")
    print(f"  Empty inputs: {result.get('error', 'No error')}")
    
    # Too long message
    long_conn_message = "C" * 400
    result = linkedin.send_connection_message("test-id", long_conn_message)
    print(f"  Long message: {result.get('error', 'No error')}")

def test_connection_status(linkedin):
    """Test connection status checking"""
    print("\n🔗 Testing Connection Status")
    print("-" * 30)
    
    try:
        status = linkedin.get_connection_status()
        print("Connection Status:")
        print(f"  Unipile available: {status.get('unipile', {}).get('available', False)}")
        print(f"  Unipile connected: {status.get('unipile', {}).get('connected', False)}")
        print(f"  LinkedIn API available: {status.get('linkedin_api', {}).get('available', False)}")
        print(f"  LinkedIn API connected: {status.get('linkedin_api', {}).get('connected', False)}")
        
        # Show connected accounts if any
        unipile_accounts = status.get('unipile', {}).get('accounts', [])
        if unipile_accounts:
            print(f"  Connected accounts: {len(unipile_accounts)}")
            for acc in unipile_accounts:
                print(f"    - {acc.get('id', 'Unknown')} ({acc.get('type', 'Unknown type')})")
        else:
            print("  No connected accounts found")
            
    except Exception as e:
        print(f"❌ Error checking connection status: {e}")

def test_bulk_messaging(linkedin):
    """Test bulk messaging functionality"""
    print("\n📬 Testing Bulk Messaging")
    print("-" * 30)
    
    # Test with invalid inputs
    result = linkedin.send_bulk_inmails([], "Subject", "Message")
    print(f"Empty recipients: {result.get('error', 'No error')}")
    
    result = linkedin.send_bulk_inmails([{"id": "test"}], "", "Message")
    print(f"Empty subject: {result.get('error', 'No error')}")
    
    result = linkedin.send_bulk_inmails([{"id": "test"}], "Subject", "")
    print(f"Empty message: {result.get('error', 'No error')}")
    
    # Test with too many recipients
    too_many_recipients = [{"id": f"test{i}"} for i in range(25)]
    result = linkedin.send_bulk_inmails(too_many_recipients, "Subject", "Message")
    print(f"Too many recipients: {result.get('error', 'No error')}")

def main():
    """Main test function"""
    print("LinkedIn Messaging Integration Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Test basic functionality
    linkedin = test_basic_functionality()
    if not linkedin:
        print("❌ Basic functionality test failed. Exiting.")
        return
    
    # Test validation functions
    test_validation_functions()
    
    # Test messaging methods
    test_messaging_methods(linkedin)
    
    # Test connection status
    test_connection_status(linkedin)
    
    # Test bulk messaging
    test_bulk_messaging(linkedin)
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print(f"Test finished at: {datetime.now().isoformat()}")
    
    print("\n📋 Next Steps:")
    print("1. Connect your LinkedIn account via Unipile dashboard")
    print("2. Update config.json with your LinkedIn API credentials (if needed)")
    print("3. Test with real LinkedIn IDs for actual messaging")

if __name__ == "__main__":
    main()
