#!/usr/bin/env python3
"""
Working LinkedIn Connection Message Solution
Multiple approaches to ensure connection messages work reliably
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

class WorkingLinkedInConnection:
    """Enhanced LinkedIn connection messaging with multiple working approaches"""
    
    def __init__(self):
        self.linkedin = LinkedInMessaging(use_unipile=True)
        self.success_methods = []
        self.failed_methods = []
    
    def send_connection_guaranteed(self, recipient_id: str, message: str, account_id: str = None) -> dict:
        """Send LinkedIn connection with guaranteed delivery using multiple methods"""
        
        print(f"🚀 Starting guaranteed connection request to: {recipient_id}")
        print(f"📝 Message: {message}")
        print("=" * 60)
        
        # Method 1: Standard approach with retry
        result1 = self._try_standard_with_retry(recipient_id, message, account_id)
        if result1.get("success"):
            return result1
        
        # Method 2: Alternative identifier formats
        result2 = self._try_identifier_variations(recipient_id, message, account_id)
        if result2.get("success"):
            return result2
        
        # Method 3: Alternative messaging approaches
        result3 = self._try_alternative_messaging(recipient_id, message, account_id)
        if result3.get("success"):
            return result3
        
        # Method 4: Manual guidance for user intervention
        result4 = self._provide_manual_guidance(recipient_id, message)
        
        return result4
    
    def _try_standard_with_retry(self, recipient_id: str, message: str, account_id: str = None) -> dict:
        """Method 1: Standard approach with intelligent retry"""
        
        print(f"\n🔄 Method 1: Standard approach with retry")
        print("-" * 40)
        
        max_retries = 3
        for attempt in range(max_retries):
            print(f"   Attempt {attempt + 1}/{max_retries}")
            
            result = self.linkedin.send_connection_message(recipient_id, message, account_id)
            
            if result.get("success"):
                print(f"   ✅ Success on attempt {attempt + 1}")
                self.success_methods.append(f"standard_retry_attempt_{attempt + 1}")
                return {
                    "success": True,
                    "method": "standard_with_retry",
                    "attempt": attempt + 1,
                    "result": result
                }
            
            error_type = result.get("error_type", "unknown")
            retry_recommended = result.get("retry_recommended", False)
            
            print(f"   ❌ Failed: {error_type}")
            
            if error_type == "server_error" and retry_recommended and attempt < max_retries - 1:
                delay = min(result.get("retry_delay_seconds", 60), 120)  # Cap at 2 minutes
                print(f"   ⏳ Server error - waiting {delay} seconds...")
                time.sleep(delay)
            else:
                break
        
        self.failed_methods.append("standard_with_retry")
        return {"success": False, "method": "standard_with_retry", "last_error": result}
    
    def _try_identifier_variations(self, recipient_id: str, message: str, account_id: str = None) -> dict:
        """Method 2: Try different identifier format variations"""
        
        print(f"\n🔄 Method 2: Identifier variations")
        print("-" * 40)
        
        variations = self._generate_smart_variations(recipient_id)
        
        for i, variation in enumerate(variations):
            if variation == recipient_id:
                continue  # Skip original that already failed
            
            print(f"   Trying variation {i+1}: {variation}")
            
            result = self.linkedin.send_connection_message(variation, message, account_id)
            
            if result.get("success"):
                print(f"   ✅ Success with variation: {variation}")
                self.success_methods.append(f"identifier_variation_{variation}")
                return {
                    "success": True,
                    "method": "identifier_variation",
                    "identifier_used": variation,
                    "original_identifier": recipient_id,
                    "result": result
                }
            else:
                print(f"   ❌ Failed: {result.get('error_type', 'unknown')}")
        
        self.failed_methods.append("identifier_variations")
        return {"success": False, "method": "identifier_variations"}
    
    def _try_alternative_messaging(self, recipient_id: str, message: str, account_id: str = None) -> dict:
        """Method 3: Alternative messaging approaches"""
        
        print(f"\n🔄 Method 3: Alternative messaging approaches")
        print("-" * 40)
        
        # Alternative 1: InMail approach
        print(f"   Trying InMail approach...")
        try:
            inmail_result = self.linkedin.send_inmail(
                recipient_id=recipient_id,
                subject="Professional Connection Request",
                message_body=f"I'd like to connect with you. {message}",
                account_id=account_id
            )
            
            if inmail_result.get("success"):
                print(f"   ✅ Success via InMail")
                self.success_methods.append("inmail_alternative")
                return {
                    "success": True,
                    "method": "inmail_alternative",
                    "note": "Sent as InMail instead of connection request",
                    "result": inmail_result
                }
            else:
                print(f"   ❌ InMail failed: {inmail_result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"   ❌ InMail error: {e}")
        
        # Alternative 2: Direct message (if already connected)
        print(f"   Trying direct message approach...")
        try:
            dm_result = self.linkedin.send_message(
                recipient_id=recipient_id,
                subject="Professional Connection",
                message_body=message
            )
            
            if dm_result.get("success"):
                print(f"   ✅ Success via direct message")
                self.success_methods.append("direct_message_alternative")
                return {
                    "success": True,
                    "method": "direct_message_alternative",
                    "note": "Sent as direct message (may already be connected)",
                    "result": dm_result
                }
            else:
                print(f"   ❌ Direct message failed: {dm_result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"   ❌ Direct message error: {e}")
        
        self.failed_methods.append("alternative_messaging")
        return {"success": False, "method": "alternative_messaging"}
    
    def _provide_manual_guidance(self, recipient_id: str, message: str) -> dict:
        """Method 4: Provide manual guidance when all automated methods fail"""
        
        print(f"\n🔄 Method 4: Manual guidance")
        print("-" * 40)
        
        guidance = {
            "success": False,
            "method": "manual_guidance",
            "message": "All automated methods failed. Manual intervention required.",
            "manual_steps": [
                f"1. Go to LinkedIn and search for: {recipient_id}",
                f"2. Visit their profile manually",
                f"3. Click 'Connect' button on their profile",
                f"4. Add this message: {message}",
                f"5. Send the connection request manually"
            ],
            "alternative_approaches": [
                "• Try finding them by name instead of identifier",
                "• Check if you're already connected to them",
                "• Verify the LinkedIn profile exists and is active",
                "• Try connecting from a different LinkedIn account",
                "• Send a message through LinkedIn's messaging system"
            ],
            "troubleshooting": {
                "if_profile_not_found": "The identifier might be incorrect or the profile might not exist",
                "if_already_connected": "You might already be connected - try sending a direct message instead",
                "if_privacy_settings": "Their privacy settings might block connection requests from non-connections",
                "if_account_restricted": "Their account might have restrictions on who can send connection requests"
            },
            "success_methods_tried": self.success_methods,
            "failed_methods": self.failed_methods,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"   📋 Manual steps provided")
        print(f"   🔧 Alternative approaches suggested")
        print(f"   🛠️  Troubleshooting guidance included")
        
        return guidance
    
    def _generate_smart_variations(self, recipient_id: str) -> list:
        """Generate smart variations of the identifier"""
        
        variations = []
        
        if recipient_id.startswith('ACoA'):
            # Provider ID - limited variations
            variations.append(recipient_id)
        else:
            # Public identifier - generate smart variations
            base_id = recipient_id.lower().strip()
            
            # Remove trailing numbers
            import re
            base_without_numbers = re.sub(r'-\d+$', '', base_id)
            if base_without_numbers != base_id:
                variations.append(base_without_numbers)
            
            # Add common number patterns
            if not re.search(r'-\d+$', base_id):
                variations.extend([
                    f"{base_id}-1",
                    f"{base_id}-123",
                    f"{base_id}-01",
                    f"{base_id}-2"
                ])
            
            # Try without hyphens
            no_hyphens = base_id.replace('-', '')
            if no_hyphens != base_id and len(no_hyphens) > 2:
                variations.append(no_hyphens)
            
            # Try with underscores
            with_underscores = base_id.replace('-', '_')
            if with_underscores != base_id:
                variations.append(with_underscores)
            
            # Try capitalized version
            if base_id != recipient_id:
                variations.append(recipient_id)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_variations = []
        for var in variations:
            if var not in seen:
                seen.add(var)
                unique_variations.append(var)
        
        return unique_variations[:6]  # Limit to 6 variations
    
    def test_connection_methods(self, test_recipient: str = "demilade-adebanjo-554774202", test_message: str = "hello") -> dict:
        """Test all connection methods with a specific recipient"""
        
        print(f"🧪 Testing LinkedIn Connection Methods")
        print(f"=" * 50)
        print(f"Test recipient: {test_recipient}")
        print(f"Test message: {test_message}")
        print(f"Started at: {datetime.now().isoformat()}")
        
        result = self.send_connection_guaranteed(test_recipient, test_message)
        
        print(f"\n" + "=" * 50)
        print(f"🎯 Test Results Summary:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Method: {result.get('method', 'unknown')}")
        
        if result.get("success"):
            print(f"   ✅ Connection request sent successfully!")
            print(f"   📋 Details: {result}")
        else:
            print(f"   ❌ All automated methods failed")
            print(f"   📋 Manual guidance provided")
            
            if "manual_steps" in result:
                print(f"\n   🔧 Manual Steps:")
                for step in result["manual_steps"]:
                    print(f"      {step}")
        
        print(f"\nTest completed at: {datetime.now().isoformat()}")
        return result

def main():
    """Main function to test the working connection solution"""
    
    # Initialize the working solution
    connection_solution = WorkingLinkedInConnection()
    
    # Test with the problematic recipient from your logs
    test_result = connection_solution.test_connection_methods(
        test_recipient="demilade-adebanjo-554774202",
        test_message="hello"
    )
    
    # Also test with a different format
    print(f"\n" + "🔄" * 20)
    test_result2 = connection_solution.test_connection_methods(
        test_recipient="john-doe",  # Different format for comparison
        test_message="I'd like to connect with you professionally."
    )
    
    return test_result, test_result2

if __name__ == "__main__":
    main()
