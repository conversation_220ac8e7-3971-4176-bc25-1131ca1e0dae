#!/usr/bin/env python3
"""
Test the enhanced error handling with disabled LinkedIn API fallback
"""

import sys
import os
import json
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def test_disabled_fallback_handling():
    """Test enhanced error handling when LinkedIn API fallback is disabled"""
    
    print("🔍 Testing Disabled LinkedIn API Fallback Handling")
    print("=" * 50)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test data from the error logs
    recipient_id = "ajunwa-marachi"
    message = "hello"
    account_id = "0gqlbGFeQ-uxqXfvVFYCrg"
    
    print(f"\n📋 Test Parameters:")
    print(f"   Recipient ID: {recipient_id}")
    print(f"   Message: {message}")
    print(f"   Account ID: {account_id}")
    
    # Check LinkedIn API configuration
    print(f"\n🔍 LinkedIn API Configuration:")
    print("-" * 30)
    print(f"   Access Token: {'✅ Set' if linkedin.access_token else '❌ Missing/Disabled'}")
    print(f"   Client ID: {'✅ Set' if linkedin.client_id else '❌ Missing/Disabled'}")
    print(f"   Person ID: {'✅ Set' if linkedin.person_id else '❌ Missing/Disabled'}")
    
    if not linkedin.access_token:
        print(f"   📝 LinkedIn API fallback is disabled (as configured)")
        print(f"   💡 This will prevent 401 Unauthorized errors")
    
    # Test the connection request call
    print(f"\n🔍 Testing Connection Request with Disabled Fallback")
    print("-" * 45)
    
    result = linkedin.send_connection_message(
        recipient_id=recipient_id,
        message=message,
        account_id=account_id
    )
    
    print(f"\n📊 Enhanced Error Response:")
    print(json.dumps(result, indent=2))
    
    # Analyze the enhanced error response
    print(f"\n🔍 Error Analysis:")
    print("-" * 20)
    
    if result.get("success"):
        print("   ✅ Connection request sent successfully!")
    else:
        error = result.get("error", "Unknown error")
        reason = result.get("reason", "No reason provided")
        solutions = result.get("solutions", [])
        suggestion = result.get("suggestion", "No suggestion provided")
        error_type = result.get("error_type", "unknown")
        retry_recommended = result.get("retry_recommended", False)
        fallback_available = result.get("fallback_available", True)
        
        print(f"   ❌ Error: {error}")
        print(f"   📝 Reason: {reason}")
        print(f"   💡 Suggestion: {suggestion}")
        print(f"   🔧 Error Type: {error_type}")
        print(f"   🔄 Retry Recommended: {retry_recommended}")
        print(f"   🔀 Fallback Available: {fallback_available}")
        
        if solutions:
            print(f"   🛠️  Solutions ({len(solutions)}):")
            for i, solution in enumerate(solutions, 1):
                print(f"      {i}. {solution}")
        
        # Check if the enhanced error handling is working correctly
        if not fallback_available and "401" not in str(error):
            print(f"\n   ✅ Enhanced error handling is working correctly!")
            print(f"   📋 Key improvements:")
            print(f"      • No 401 Unauthorized errors (fallback disabled)")
            print(f"      • Clear explanation of server error vs fallback issue")
            print(f"      • Specific guidance for retry strategy")
            print(f"      • Proper error classification")
        elif "401" in str(error):
            print(f"\n   ❌ Still getting 401 errors - fallback not properly disabled")
        else:
            print(f"\n   ⚠️  Unexpected error pattern")
    
    # Test retry recommendation
    print(f"\n🔍 Testing Retry Recommendation")
    print("-" * 30)
    
    if result.get("retry_recommended"):
        retry_delay = result.get("retry_delay_seconds", 120)
        print(f"   ✅ Retry is recommended after {retry_delay} seconds")
        print(f"   💡 This is the correct guidance for server errors")
        print(f"   📝 User should wait {retry_delay} seconds and try again")
    else:
        print(f"   ❌ Retry not recommended - this might be incorrect for server errors")
    
    # Test different scenarios
    print(f"\n🔍 Testing Different Error Scenarios")
    print("-" * 35)
    
    test_scenarios = [
        {
            "name": "Valid identifier format",
            "recipient_id": "john-doe-123",
            "expected": "Should show server error with retry guidance"
        },
        {
            "name": "Invalid identifier format", 
            "recipient_id": "invalid@format",
            "expected": "Should show validation error"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n   Testing: {scenario['name']}")
        print(f"   Recipient: {scenario['recipient_id']}")
        print(f"   Expected: {scenario['expected']}")
        
        test_result = linkedin.send_connection_message(
            recipient_id=scenario['recipient_id'],
            message="Test message",
            account_id=account_id
        )
        
        if test_result.get("success"):
            print(f"   ✅ Unexpected success")
        else:
            error_msg = test_result.get("error", "")
            fallback_available = test_result.get("fallback_available", True)
            
            if "500" in error_msg and not fallback_available:
                print(f"   ✅ Correct server error handling (no 401 fallback)")
            elif "format" in error_msg.lower() or "invalid" in error_msg.lower():
                print(f"   ✅ Correct validation error")
            elif "401" in error_msg:
                print(f"   ❌ Still getting 401 errors - fallback not disabled")
            else:
                print(f"   ⚠️  Other error: {error_msg[:50]}...")
    
    print(f"\n" + "=" * 50)
    print("🎯 Test Summary:")
    
    if result.get("success"):
        print("✅ Connection request is working correctly")
    else:
        print("❌ Connection request failed (expected due to server error):")
        
        # Check if the improvements are working
        improvements = []
        if not result.get("fallback_available", True):
            improvements.append("✅ LinkedIn API fallback properly disabled")
        if "401" not in str(result.get("error", "")):
            improvements.append("✅ No 401 Unauthorized errors")
        if result.get("retry_recommended"):
            improvements.append("✅ Retry guidance provided")
        if result.get("solutions"):
            improvements.append("✅ Multiple solutions offered")
        
        if improvements:
            print("   Improvements working:")
            for improvement in improvements:
                print(f"      {improvement}")
        
        # Check for remaining issues
        issues = []
        if "401" in str(result.get("error", "")):
            issues.append("❌ Still getting 401 errors")
        if not result.get("retry_recommended"):
            issues.append("❌ No retry guidance for server error")
        
        if issues:
            print("   Remaining issues:")
            for issue in issues:
                print(f"      {issue}")
        else:
            print("   ✅ All issues resolved!")
    
    print(f"\nTest completed at: {datetime.now().isoformat()}")
    return result

def test_config_validation():
    """Test that the config changes are working correctly"""
    print("\n🔍 Testing Config Validation")
    print("=" * 30)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    print(f"   Unipile Client: {'✅ Available' if linkedin.unipile_client else '❌ Missing'}")
    print(f"   LinkedIn API Access Token: {'✅ Set' if linkedin.access_token else '❌ Disabled'}")
    print(f"   LinkedIn API Client ID: {'✅ Set' if linkedin.client_id else '❌ Disabled'}")
    
    # Check if the config was updated correctly
    config_path = "integrations/linkedin_integration/config.json"
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        linkedin_api_config = config.get("linkedin_api", {})
        enabled = linkedin_api_config.get("enabled", True)
        
        print(f"   Config LinkedIn API Enabled: {enabled}")
        
        if not enabled:
            print(f"   ✅ LinkedIn API is properly disabled in config")
        else:
            print(f"   ⚠️  LinkedIn API is still enabled in config")
            
    except Exception as e:
        print(f"   ❌ Error reading config: {e}")

if __name__ == "__main__":
    test_disabled_fallback_handling()
    test_config_validation()
