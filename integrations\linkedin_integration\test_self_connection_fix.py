#!/usr/bin/env python3
"""
Test the self-connection validation fix
Demonstrates the issue and shows proper usage
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def test_self_connection_validation():
    """Test the self-connection validation"""
    print("🔧 Testing Self-Connection Validation Fix")
    print("=" * 50)
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Get the current account ID
    account_id = linkedin._get_linkedin_account_id()
    print(f"Your LinkedIn Account ID: {account_id}")
    
    if not account_id:
        print("❌ No LinkedIn account ID found. Please check your Unipile connection.")
        return
    
    # Test 1: Try to send connection request to self (should fail)
    print("\n1. Testing connection request to self (should fail)...")
    result = linkedin.send_connection_message(account_id, "Hello myself!")
    
    if result.get("error"):
        print("✅ Self-connection properly blocked!")
        print(f"   Error: {result['error']}")
        print(f"   Suggestion: {result.get('suggestion', 'N/A')}")
    else:
        print("❌ Self-connection was not blocked (this is a bug)")
    
    # Test 2: Show valid examples
    print("\n2. Getting valid LinkedIn identifier examples...")
    examples = linkedin.get_valid_linkedin_examples()
    
    print("Valid LinkedIn identifier examples (NOT your account):")
    for example in examples["public_identifiers"]:
        print(f"   • {example}")
    
    print(f"\nProvider ID format: {examples['provider_id_format']}")
    print(f"Your account ID (DON'T use as recipient): {examples['your_account_id']}")
    
    # Test 3: Test with valid example identifiers
    print("\n3. Testing with valid example identifiers...")
    
    test_cases = [
        ("john-doe", "Should work (if profile exists)"),
        ("jane-smith-123", "Should work (if profile exists)"),
        ("invalid-format@email", "Should fail validation"),
        ("", "Should fail validation"),
        (account_id, "Should fail self-connection check")
    ]
    
    for test_id, description in test_cases:
        print(f"\n   Testing: '{test_id}' - {description}")
        result = linkedin.send_connection_message(test_id, "Test connection message")
        
        if result.get("success"):
            print("   ✅ Would send successfully")
        else:
            error = result.get('error', 'Unknown error')
            print(f"   ❌ Blocked: {error[:80]}...")

def show_proper_usage_examples():
    """Show examples of proper LinkedIn messaging usage"""
    print("\n📝 Proper Usage Examples")
    print("=" * 30)
    
    print("✅ CORRECT - Using someone else's LinkedIn identifier:")
    print("""
# Example 1: Using public identifier from LinkedIn URL
# URL: https://linkedin.com/in/john-doe
linkedin.send_connection_message("john-doe", "Hi John, I'd love to connect!")

# Example 2: Using provider ID (if you have it)
linkedin.send_connection_message("ACoAAABCDEF123456789", "Hello! Let's connect.")

# Example 3: Sending InMail
linkedin.send_inmail(
    recipient_id="jane-smith",
    subject="Professional Opportunity",
    message="Hi Jane, I have an exciting opportunity to discuss..."
)
""")
    
    print("❌ INCORRECT - Using your own account ID:")
    print("""
# This will fail with 'Cannot send connection request to yourself'
linkedin.send_connection_message("0gqlbGFeQ-uxqXfvVFYCrg", "Hello!")
""")
    
    print("💡 How to find valid LinkedIn identifiers:")
    print("""
1. Go to someone else's LinkedIn profile (not your own)
2. Copy the profile URL: https://linkedin.com/in/john-doe
3. Extract the identifier: 'john-doe'
4. Use it in your messaging code
""")

def demonstrate_error_improvements():
    """Demonstrate the improved error messages"""
    print("\n🔍 Error Message Improvements")
    print("=" * 35)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    account_id = linkedin._get_linkedin_account_id()
    
    print("Before fix: Generic HTTP 500 error")
    print("After fix: Clear, actionable error messages")
    
    # Test self-connection error
    result = linkedin.send_connection_message(account_id, "Test message")
    
    print(f"\n📋 Enhanced Error Response:")
    print(f"   Error: {result.get('error')}")
    print(f"   Suggestion: {result.get('suggestion')}")
    print(f"   Your Account: {result.get('your_account_id')}")
    print(f"   Attempted Recipient: {result.get('attempted_recipient')}")
    
    print("\n✅ Benefits of the fix:")
    print("   • Clear explanation of what went wrong")
    print("   • Specific guidance on how to fix it")
    print("   • Shows your account ID vs attempted recipient")
    print("   • Prevents unnecessary API calls")

def main():
    """Main test function"""
    print("LinkedIn Self-Connection Validation Fix Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    try:
        test_self_connection_validation()
        show_proper_usage_examples()
        demonstrate_error_improvements()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        
        print("\n🎯 Summary of the fix:")
        print("1. ✅ Self-connection attempts are now blocked with clear error messages")
        print("2. ✅ Proper validation prevents HTTP 500 errors")
        print("3. ✅ Helpful suggestions guide users to correct usage")
        print("4. ✅ Examples provided for valid LinkedIn identifiers")
        
        print("\n📋 Next steps:")
        print("1. Use someone else's LinkedIn public identifier (not your own)")
        print("2. Get identifiers from LinkedIn profile URLs: linkedin.com/in/[identifier]")
        print("3. Test with real LinkedIn profiles for actual messaging")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\nTest completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
