#!/usr/bin/env python3
"""
LinkedIn Messaging Examples
Demonstrates how to use the enhanced LinkedIn messaging functionality
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def example_send_inmail():
    """Example: Send a professional InMail"""
    print("📧 Example: Sending InMail")
    print("-" * 30)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Example InMail (replace with real LinkedIn ID)
    recipient_id = "john-doe"  # Replace with actual LinkedIn public identifier or provider ID
    subject = "Professional Opportunity in Tech"
    message = """Hello John,

I hope this message finds you well. I came across your profile and was impressed by your experience in software development.

We have an exciting opportunity at our company that I believe would be a perfect fit for your skills. Would you be interested in a brief conversation to discuss this further?

Best regards,
[Your Name]"""

    # Send the InMail
    result = linkedin.send_inmail(recipient_id, subject, message)
    
    if result.get("success"):
        print("✅ InMail sent successfully!")
        print(f"Method used: {result.get('method')}")
        print(f"Timestamp: {result.get('timestamp')}")
    else:
        print(f"❌ Failed to send InMail: {result.get('error')}")
    
    return result

def example_send_connection_request():
    """Example: Send a connection request with message"""
    print("\n🤝 Example: Sending Connection Request")
    print("-" * 40)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Example connection request (replace with real LinkedIn ID)
    recipient_id = "jane-smith"  # Replace with actual LinkedIn public identifier or provider ID
    message = """Hi Jane,

I noticed we both work in the tech industry and have mutual connections. I'd love to connect and potentially collaborate on future projects.

Looking forward to connecting!"""

    # Send the connection request
    result = linkedin.send_connection_message(recipient_id, message)
    
    if result.get("success"):
        print("✅ Connection request sent successfully!")
        print(f"Method used: {result.get('method')}")
        print(f"Timestamp: {result.get('timestamp')}")
    else:
        print(f"❌ Failed to send connection request: {result.get('error')}")
    
    return result

def example_bulk_inmails():
    """Example: Send bulk InMails with personalization"""
    print("\n📬 Example: Bulk InMail Campaign")
    print("-" * 35)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Example recipients (replace with real data)
    recipients = [
        {
            "id": "john-doe",
            "name": "John",
            "company": "Tech Corp",
            "position": "Software Engineer"
        },
        {
            "id": "jane-smith", 
            "name": "Jane",
            "company": "Innovation Inc",
            "position": "Product Manager"
        }
    ]
    
    # Template with personalization placeholders
    subject_template = "Opportunity for {position} at {company}"
    message_template = """Hello {name},

I hope you're doing well at {company}. I came across your profile and was impressed by your work as a {position}.

We have an exciting opportunity that might interest you. Would you be open to a brief conversation?

Best regards,
[Your Name]"""

    # Send bulk InMails
    result = linkedin.send_bulk_inmails(
        recipients=recipients,
        subject=subject_template,
        message_template=message_template,
        delay=3.0  # 3 second delay between messages
    )
    
    print(f"📊 Bulk Campaign Results:")
    print(f"Total recipients: {result.get('total_recipients', 0)}")
    print(f"Successful sends: {result.get('successful_sends', 0)}")
    print(f"Failed sends: {result.get('failed_sends', 0)}")
    print(f"Success rate: {result.get('success_rate', 0):.1f}%")
    
    # Show individual results
    for i, res in enumerate(result.get('results', []), 1):
        status_icon = "✅" if res.get('status') == 'success' else "❌"
        print(f"  {status_icon} Recipient {i}: {res.get('recipient_id')} - {res.get('status')}")
    
    return result

def example_check_connection_status():
    """Example: Check connection status and account info"""
    print("\n🔗 Example: Connection Status Check")
    print("-" * 38)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Get connection status
    status = linkedin.get_connection_status()
    
    print("Connection Status:")
    print(f"  🔧 Unipile API: {'✅ Connected' if status.get('unipile', {}).get('connected') else '❌ Not connected'}")
    print(f"  🔗 LinkedIn API: {'✅ Connected' if status.get('linkedin_api', {}).get('connected') else '❌ Not connected'}")
    
    # Show connected accounts
    unipile_accounts = status.get('unipile', {}).get('accounts', [])
    if unipile_accounts:
        print(f"\n📱 Connected LinkedIn Accounts ({len(unipile_accounts)}):")
        for acc in unipile_accounts:
            print(f"  • Account ID: {acc.get('id')}")
            print(f"    Type: {acc.get('type')}")
            if acc.get('name'):
                print(f"    Name: {acc.get('name')}")
    else:
        print("\n⚠️  No LinkedIn accounts connected via Unipile")
        print("   Please connect your account via the Unipile dashboard")
    
    return status

def example_message_validation():
    """Example: Demonstrate message validation features"""
    print("\n✅ Example: Message Validation")
    print("-" * 32)
    
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test various validation scenarios
    test_cases = [
        {
            "name": "Valid InMail",
            "recipient": "john-doe",
            "subject": "Professional Opportunity",
            "message": "Hello! I'd like to discuss a professional opportunity."
        },
        {
            "name": "Empty recipient",
            "recipient": "",
            "subject": "Test Subject",
            "message": "Test message"
        },
        {
            "name": "Too long subject",
            "recipient": "john-doe",
            "subject": "A" * 250,  # Too long
            "message": "Test message"
        },
        {
            "name": "Too long message",
            "recipient": "john-doe", 
            "subject": "Test Subject",
            "message": "B" * 2000  # Too long
        }
    ]
    
    for test in test_cases:
        print(f"\n🧪 Testing: {test['name']}")
        result = linkedin.send_inmail(
            test['recipient'],
            test['subject'],
            test['message']
        )
        
        if result.get('success'):
            print(f"  ✅ Would send successfully")
        else:
            print(f"  ❌ Validation error: {result.get('error')}")

def main():
    """Main function to run all examples"""
    print("LinkedIn Messaging Examples")
    print("=" * 50)
    print(f"Started at: {datetime.now().isoformat()}")
    
    # Check connection status first
    example_check_connection_status()
    
    # Demonstrate message validation
    example_message_validation()
    
    print("\n" + "=" * 50)
    print("📝 READY TO USE EXAMPLES")
    print("=" * 50)
    print("\n⚠️  The following examples are ready to use with real LinkedIn IDs:")
    print("   (Uncomment and replace with actual LinkedIn IDs to test)")
    
    # Uncomment these lines and replace with real LinkedIn IDs to test actual messaging
    # example_send_inmail()
    # example_send_connection_request() 
    # example_bulk_inmails()
    
    print("\n🎯 To use these examples:")
    print("1. Replace 'john-doe', 'jane-smith' with real LinkedIn public identifiers")
    print("2. Or use LinkedIn provider IDs (format: ACoAAABCDEF...)")
    print("3. Uncomment the example functions above")
    print("4. Run this script again")
    
    print(f"\nCompleted at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
