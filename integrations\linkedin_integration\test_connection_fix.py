#!/usr/bin/env python3
"""
Test the LinkedIn connection request fix
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def test_connection_request_fix():
    """Test the enhanced connection request functionality"""
    print("🔧 Testing LinkedIn Connection Request Fix")
    print("=" * 50)
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test connection status first
    print("1. Checking connection status...")
    status = linkedin.get_connection_status()
    
    unipile_connected = status.get('unipile', {}).get('connected', False)
    print(f"   Unipile connected: {'✅' if unipile_connected else '❌'}")
    
    if not unipile_connected:
        print("   ⚠️  No Unipile connection - cannot test actual API calls")
        return
    
    # Get connected account info
    accounts = status.get('unipile', {}).get('accounts', [])
    if accounts:
        account_id = accounts[0].get('id')
        print(f"   Using account ID: {account_id}")
    else:
        print("   ❌ No account ID found")
        return
    
    # Test the problematic case from the logs
    print("\n2. Testing connection request with public identifier...")
    test_recipient = "ajunwa-nwamaka"  # The one that failed in the logs
    test_message = "Hi! I'd like to connect with you on LinkedIn."
    
    print(f"   Recipient: {test_recipient}")
    print(f"   Message: {test_message}")
    
    # This should now handle the profile lookup failure gracefully
    result = linkedin.send_connection_message(test_recipient, test_message)
    
    print(f"\n3. Connection request result:")
    if result.get("success"):
        print("   ✅ Connection request sent successfully!")
        print(f"   Method: {result.get('method')}")
        print(f"   Endpoint used: {result.get('result', {}).get('endpoint_used', 'Unknown')}")
    else:
        print(f"   ❌ Connection request failed: {result.get('error')}")
        
        # Check if it's a validation error vs API error
        error_msg = result.get('error', '')
        if 'Valid recipient_id is required' in error_msg:
            print("   → This is a validation error (expected)")
        elif 'Connection message too long' in error_msg:
            print("   → This is a message length error (expected)")
        elif 'HTTP 422' in error_msg or 'Unprocessable Entity' in error_msg:
            print("   → This is an API format error - the fix should handle this better")
        else:
            print("   → This is an unexpected error")
    
    # Test with different identifier formats
    print("\n4. Testing different identifier formats...")
    
    test_cases = [
        ("valid-linkedin-id", "Should work with valid format"),
        ("john-doe-123", "Should work with numbers"),
        ("ACoAAABCDEF123456789", "Should work with provider ID format"),
        ("", "Should fail validation"),
        ("a", "Should fail validation (too short)")
    ]
    
    for test_id, description in test_cases:
        print(f"\n   Testing: {test_id} - {description}")
        result = linkedin.send_connection_message(test_id, "Test message")
        
        if result.get("success"):
            print(f"   ✅ Would send successfully")
        else:
            error = result.get('error', 'Unknown error')
            print(f"   ❌ Failed: {error[:100]}...")

def main():
    """Main test function"""
    print("LinkedIn Connection Request Fix Test")
    print("=" * 50)
    
    try:
        test_connection_request_fix()
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
    
    print("\n📋 Summary of fixes:")
    print("1. Enhanced profile lookup with multiple endpoint attempts")
    print("2. Graceful handling of profile lookup failures")
    print("3. Multiple connection request formats and endpoints")
    print("4. Better error messages and debugging info")
    print("5. Fallback to original identifier if conversion fails")

if __name__ == "__main__":
    main()
