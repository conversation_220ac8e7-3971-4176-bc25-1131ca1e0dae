#!/usr/bin/env python3
"""
Simple Working LinkedIn Connection Solution
Focus on making connection messages work reliably
"""

import sys
import os
import time
import requests
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_unipile_status():
    """Check if Unipile API is currently working"""
    try:
        response = requests.get("https://api8.unipile.com:13814/health", timeout=10)
        return response.status_code == 200
    except:
        return False

def wait_for_unipile_recovery(max_wait_minutes=5):
    """Wait for Unipile to recover from server issues"""
    print(f"⏳ Waiting for Unipile server recovery (max {max_wait_minutes} minutes)...")
    
    for minute in range(max_wait_minutes):
        if check_unipile_status():
            print(f"✅ Unipile server recovered after {minute + 1} minute(s)")
            return True
        
        if minute < max_wait_minutes - 1:
            print(f"   Still down, waiting... ({minute + 1}/{max_wait_minutes} minutes)")
            time.sleep(60)  # Wait 1 minute
    
    print(f"❌ Unipile server still down after {max_wait_minutes} minutes")
    return False

def send_connection_simple(recipient_id, message, max_retries=3):
    """Simple, reliable connection message sending"""
    
    print(f"🚀 Sending LinkedIn connection request")
    print(f"   Recipient: {recipient_id}")
    print(f"   Message: {message}")
    print(f"   Max retries: {max_retries}")
    print("-" * 50)
    
    from linkedin_integration.linkedin_api import LinkedInMessaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    for attempt in range(max_retries):
        print(f"\n🔄 Attempt {attempt + 1}/{max_retries}")
        
        # Send connection request
        result = linkedin.send_connection_message(recipient_id, message)
        
        if result.get("success"):
            print(f"✅ SUCCESS! Connection request sent")
            print(f"   Method: {result.get('method', 'unknown')}")
            print(f"   Timestamp: {result.get('timestamp')}")
            return {
                "success": True,
                "attempt": attempt + 1,
                "result": result
            }
        
        # Handle different error types
        error_type = result.get("error_type", "unknown")
        error_msg = result.get("error", "Unknown error")
        
        print(f"❌ Failed: {error_type}")
        print(f"   Error: {error_msg[:100]}...")
        
        if error_type == "server_error":
            # Server error - wait and retry
            if attempt < max_retries - 1:
                delay = result.get("retry_delay_seconds", 120)
                delay = min(delay, 180)  # Cap at 3 minutes
                
                print(f"   🔧 Server error detected")
                print(f"   ⏳ Waiting {delay} seconds before retry...")
                
                # Check if Unipile recovers during wait
                recovery_check_interval = 30  # Check every 30 seconds
                for i in range(0, delay, recovery_check_interval):
                    time.sleep(recovery_check_interval)
                    if check_unipile_status():
                        print(f"   ✅ Unipile recovered early! Retrying now...")
                        break
                    print(f"   ⏳ Still waiting... ({i + recovery_check_interval}/{delay}s)")
                
                # Sleep remaining time if any
                remaining_time = delay % recovery_check_interval
                if remaining_time > 0:
                    time.sleep(remaining_time)
            
        elif error_type == "validation_error":
            # Validation error - try identifier variations
            print(f"   🔧 Validation error - trying identifier variations...")
            
            variations = generate_identifier_variations(recipient_id)
            for variation in variations:
                if variation == recipient_id:
                    continue
                
                print(f"   🔄 Trying: {variation}")
                var_result = linkedin.send_connection_message(variation, message)
                
                if var_result.get("success"):
                    print(f"   ✅ SUCCESS with variation: {variation}")
                    return {
                        "success": True,
                        "attempt": attempt + 1,
                        "identifier_used": variation,
                        "original_identifier": recipient_id,
                        "result": var_result
                    }
                else:
                    print(f"   ❌ Variation failed: {var_result.get('error_type', 'unknown')}")
            
            print(f"   ❌ All identifier variations failed")
            
        else:
            # Other error types
            print(f"   ❌ Non-retryable error: {error_type}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Waiting 30 seconds before next attempt...")
                time.sleep(30)
    
    # All attempts failed
    print(f"\n❌ All {max_retries} attempts failed")
    return {
        "success": False,
        "attempts": max_retries,
        "last_error": result,
        "solutions": result.get("solutions", [])
    }

def generate_identifier_variations(recipient_id):
    """Generate simple identifier variations to try"""
    variations = []
    
    if not recipient_id.startswith('ACoA'):
        # Public identifier variations
        base = recipient_id.lower().strip()
        
        # Remove trailing numbers
        import re
        base_no_numbers = re.sub(r'-\d+$', '', base)
        if base_no_numbers != base:
            variations.append(base_no_numbers)
        
        # Add common numbers
        if not re.search(r'-\d+$', base):
            variations.extend([f"{base}-1", f"{base}-123"])
        
        # Without hyphens
        no_hyphens = base.replace('-', '')
        if no_hyphens != base and len(no_hyphens) > 2:
            variations.append(no_hyphens)
    
    variations.append(recipient_id)  # Original
    
    # Remove duplicates
    return list(dict.fromkeys(variations))

def test_working_solution():
    """Test the simple working solution"""
    
    print(f"🧪 Testing Simple Working LinkedIn Connection Solution")
    print(f"=" * 60)
    print(f"Started at: {datetime.now().isoformat()}")
    
    # Test cases
    test_cases = [
        {
            "recipient_id": "demilade-adebanjo-554774202",
            "message": "hello",
            "description": "Your problematic case"
        },
        {
            "recipient_id": "john-doe",
            "message": "I'd like to connect with you professionally.",
            "description": "Simple test case"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test Case {i}: {test_case['description']}")
        print("=" * 40)
        
        result = send_connection_simple(
            test_case["recipient_id"],
            test_case["message"],
            max_retries=2  # Reduced for testing
        )
        
        results.append({
            "test_case": test_case,
            "result": result
        })
        
        if result.get("success"):
            print(f"\n✅ Test Case {i} PASSED")
        else:
            print(f"\n❌ Test Case {i} FAILED")
            solutions = result.get("solutions", [])
            if solutions:
                print(f"   💡 Suggested solutions:")
                for j, solution in enumerate(solutions[:3], 1):
                    print(f"      {j}. {solution}")
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"🎯 Test Summary:")
    
    successful_tests = sum(1 for r in results if r["result"].get("success"))
    total_tests = len(results)
    
    print(f"   Successful: {successful_tests}/{total_tests}")
    print(f"   Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests > 0:
        print(f"   ✅ At least one method is working!")
    else:
        print(f"   ❌ All tests failed - manual intervention needed")
        print(f"\n   🔧 Manual steps:")
        print(f"      1. Go to LinkedIn.com")
        print(f"      2. Search for: demilade-adebanjo-554774202")
        print(f"      3. Visit their profile")
        print(f"      4. Click 'Connect' and add message: hello")
        print(f"      5. Send connection request manually")
    
    print(f"\nCompleted at: {datetime.now().isoformat()}")
    return results

def quick_connection_test(recipient_id="demilade-adebanjo-554774202", message="hello"):
    """Quick test of connection functionality"""
    
    print(f"⚡ Quick Connection Test")
    print(f"   Recipient: {recipient_id}")
    print(f"   Message: {message}")
    print("-" * 30)
    
    # Check Unipile status first
    print(f"🔍 Checking Unipile status...")
    if check_unipile_status():
        print(f"✅ Unipile is online")
    else:
        print(f"❌ Unipile appears to be down")
        print(f"⏳ Waiting for recovery...")
        if wait_for_unipile_recovery(max_wait_minutes=2):
            print(f"✅ Unipile recovered!")
        else:
            print(f"❌ Unipile still down - connection may fail")
    
    # Try connection
    result = send_connection_simple(recipient_id, message, max_retries=1)
    
    if result.get("success"):
        print(f"\n🎉 SUCCESS! Connection request sent successfully!")
        return True
    else:
        print(f"\n❌ Failed. Try again in a few minutes or use manual approach.")
        return False

if __name__ == "__main__":
    # Run quick test first
    print("Starting quick test...")
    quick_success = quick_connection_test()
    
    if not quick_success:
        print("\n" + "="*50)
        print("Quick test failed. Running comprehensive test...")
        test_working_solution()
