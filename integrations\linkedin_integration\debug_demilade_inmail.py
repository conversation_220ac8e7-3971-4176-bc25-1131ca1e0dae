#!/usr/bin/env python3
"""
Debug the specific InMail issue with demilade-adebanjo-*********
"""

import sys
import os
import json
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def debug_demilade_inmail():
    """Debug the specific InMail issue"""
    
    print("🔍 Debugging Demilade InMail Issue")
    print("=" * 40)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test data from the error logs
    recipient_id = "demilade-adebanjo-*********"
    subject = "Testing"
    message_body = "Hello, how are you this morning? just confirm that the linkedin messaging is working, thank you."
    account_id = "0gqlbGFeQ-uxqXfvVFYCrg"
    
    print(f"\n📋 Test Parameters:")
    print(f"   Recipient ID: {recipient_id}")
    print(f"   Subject: {subject}")
    print(f"   Message: {message_body[:50]}...")
    print(f"   Account ID: {account_id}")
    
    # Step 1: Test identifier validation
    print(f"\n🔍 Step 1: Identifier Validation")
    print("-" * 30)
    
    from linkedin_integration.linkedin_api import validate_linkedin_id
    is_valid = validate_linkedin_id(recipient_id)
    print(f"   Valid LinkedIn ID: {is_valid}")
    
    # Step 2: Test identifier resolution
    print(f"\n🔍 Step 2: Identifier Resolution")
    print("-" * 30)
    
    if linkedin.unipile_client:
        print("   Testing profile lookup...")
        profile_result = linkedin.unipile_client.get_linkedin_profile(account_id, recipient_id)
        print(f"   Profile lookup result: {json.dumps(profile_result, indent=2)}")
        
        if "error" not in profile_result:
            provider_id = profile_result.get("provider_id")
            print(f"   ✅ Resolved provider ID: {provider_id}")
        else:
            print(f"   ❌ Profile lookup failed: {profile_result.get('error')}")
    else:
        print("   ❌ Unipile client not available")
    
    # Step 3: Test direct InMail call
    print(f"\n🔍 Step 3: Direct InMail Test")
    print("-" * 30)
    
    result = linkedin.send_inmail(
        recipient_id=recipient_id,
        subject=subject,
        message_body=message_body,
        account_id=account_id
    )
    
    print(f"   InMail Result:")
    print(json.dumps(result, indent=2))
    
    # Step 4: Analyze the error
    print(f"\n🔍 Step 4: Error Analysis")
    print("-" * 30)
    
    if result.get("success"):
        print("   ✅ InMail sent successfully!")
    else:
        error = result.get("error", "Unknown error")
        print(f"   ❌ InMail failed: {error}")
        
        # Check if it's a 404 error (identifier not found)
        if "404" in str(error) or "Not Found" in str(error):
            print(f"   📝 Analysis: The identifier '{recipient_id}' was not found")
            print(f"   💡 Possible causes:")
            print(f"      1. The LinkedIn profile doesn't exist")
            print(f"      2. The public identifier is incorrect")
            print(f"      3. The profile is private or restricted")
            print(f"      4. The identifier format is wrong")
            
            # Test alternative formats
            print(f"\n   🔄 Testing alternative formats:")
            
            # Try without the numbers at the end
            alt_id_1 = "demilade-adebanjo"
            print(f"      Testing: {alt_id_1}")
            alt_result_1 = linkedin.unipile_client.get_linkedin_profile(account_id, alt_id_1) if linkedin.unipile_client else {"error": "No client"}
            print(f"      Result: {'✅ Found' if 'error' not in alt_result_1 else '❌ Not found'}")
            
            # Try with different number format
            alt_id_2 = "demilade-adebanjo-554774"
            print(f"      Testing: {alt_id_2}")
            alt_result_2 = linkedin.unipile_client.get_linkedin_profile(account_id, alt_id_2) if linkedin.unipile_client else {"error": "No client"}
            print(f"      Result: {'✅ Found' if 'error' not in alt_result_2 else '❌ Not found'}")
            
        elif "401" in str(error) or "Unauthorized" in str(error):
            print(f"   📝 Analysis: LinkedIn API authorization failed")
            print(f"   💡 Possible causes:")
            print(f"      1. Access token is expired or invalid")
            print(f"      2. LinkedIn API credentials are not configured")
            print(f"      3. Insufficient permissions for messaging")
            
            # Check LinkedIn API configuration
            print(f"\n   🔧 LinkedIn API Configuration:")
            print(f"      Access Token: {'✅ Set' if linkedin.access_token else '❌ Missing'}")
            print(f"      Client ID: {'✅ Set' if linkedin.client_id else '❌ Missing'}")
            print(f"      Person ID: {'✅ Set' if linkedin.person_id else '❌ Missing'}")
    
    # Step 5: Test with a known working identifier
    print(f"\n🔍 Step 5: Test with Known Working Identifier")
    print("-" * 30)
    
    # Use the identifier that worked before
    test_recipient = "mayowa-ade"
    print(f"   Testing with: {test_recipient}")
    
    test_result = linkedin.send_inmail(
        recipient_id=test_recipient,
        subject="Test",
        message_body="Test message",
        account_id=account_id
    )
    
    if test_result.get("success"):
        print(f"   ✅ Test InMail successful - system is working")
        print(f"   📝 Conclusion: The issue is specifically with '{recipient_id}'")
    else:
        print(f"   ❌ Test InMail also failed: {test_result.get('error')}")
        print(f"   📝 Conclusion: There's a broader system issue")
    
    print(f"\n" + "=" * 40)
    print("🎯 Debug Summary:")
    
    if result.get("success"):
        print("✅ InMail is working correctly")
    else:
        print("❌ InMail failed - specific issues identified:")
        if "404" in str(result.get("error", "")):
            print("   • Identifier not found - check LinkedIn profile exists")
        if "401" in str(result.get("error", "")):
            print("   • Authorization failed - check LinkedIn API credentials")
        
        print("\n💡 Recommended actions:")
        print("1. Verify the LinkedIn profile exists: https://linkedin.com/in/demilade-adebanjo-*********")
        print("2. Try alternative identifier formats")
        print("3. Check LinkedIn API credentials if using fallback")
        print("4. Use a known working identifier to test system health")
    
    print(f"\nTest completed at: {datetime.now().isoformat()}")
    return result

if __name__ == "__main__":
    debug_demilade_inmail()
