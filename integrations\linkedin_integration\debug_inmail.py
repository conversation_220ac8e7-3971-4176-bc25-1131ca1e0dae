#!/usr/bin/env python3
"""
Debug script to test InMail identifier resolution
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging

def debug_inmail_call():
    """Debug the InMail call to see which method is being used"""
    print("🔍 Debugging InMail Call")
    print("=" * 30)
    
    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)
    
    # Test the exact same call that's failing
    print("Testing InMail to mayowa-ade...")
    result = linkedin.send_inmail(
        recipient_id="mayowa-ade",
        subject="Test",
        message_body="hello, i hope this mail finds you well."
    )
    
    print(f"Result: {result}")

if __name__ == "__main__":
    debug_inmail_call()
