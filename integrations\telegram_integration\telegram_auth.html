<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0088cc;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #0088cc;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #0088cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #006699;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #0088cc;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Telegram Bot Setup</h1>
        
        <div class="info">
            <strong>ℹ️ Info:</strong> Telegram bots are easy to set up and free to use. 
            You'll need to create a bot through BotFather and get your bot token.
        </div>

        <div class="step">
            <h3>Step 1: Create Your Telegram Bot</h3>
            <p>1. Open Telegram and search for <strong>@BotFather</strong></p>
            <p>2. Start a chat with BotFather and send <code>/newbot</code></p>
            <p>3. Follow the instructions to choose a name and username for your bot</p>
            <p>4. BotFather will give you a bot token - copy this token</p>
            <p>5. (Optional) Use <code>/setdescription</code> and <code>/setabouttext</code> to customize your bot</p>
        </div>

        <div class="step">
            <h3>🚀 Step 2: Connect via Unipile (Recommended)</h3>
            <div class="info">
                <strong>Unipile provides unified access to Telegram with simplified authentication and appears in your Unipile dashboard.</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect via Unipile</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>🤖 Step 3: Alternative - Bot Token Method</h3>
            <div class="info">
                <strong>Use this method if you prefer direct bot API access (won't appear in Unipile dashboard).</strong>
            </div>

            <form id="telegramConfigForm">
                <div class="form-group">
                    <label for="botToken">Bot Token:</label>
                    <input type="password" id="botToken" name="botToken"
                           placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz">
                </div>

                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>

            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: Bot Information</h3>
            <p>Once configured, you can get information about your bot:</p>
            <button type="button" onclick="getBotInfo()">Get Bot Info</button>
            <div id="botInfoResult"></div>
        </div>



        <div class="step">
            <h3>Step 5: Test Your Bot</h3>

            <!-- Single Message -->
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📱 Single Message</h4>
                <div class="form-group">
                    <label for="testChatId">Chat ID or Username:</label>
                    <input type="text" id="testChatId" placeholder="@username or chat_id">
                </div>
                <div class="form-group">
                    <label for="testMessage">Test Message:</label>
                    <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from your Telegram bot."></textarea>
                </div>
                <button type="button" onclick="sendTestMessage()">Send Test Message</button>
                <div id="testResult"></div>
            </div>

            <!-- Bulk Messaging -->
            <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📢 Bulk Messaging</h4>
                <div class="form-group">
                    <label for="bulkChatIds">Chat IDs or Usernames (one per line):</label>
                    <textarea id="bulkChatIds" rows="4" placeholder="@username1&#10;@username2&#10;123456789"></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkMessage">Bulk Message:</label>
                    <textarea id="bulkMessage" rows="3" placeholder="Hello! This is a bulk message from your Telegram bot."></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkDelay">Delay between messages (seconds):</label>
                    <input type="number" id="bulkDelay" value="1" min="0.5" max="10" step="0.5">
                </div>
                <button type="button" onclick="sendBulkMessages()">Send Bulk Messages</button>
                <div id="bulkResult"></div>
            </div>
        </div>

        <div class="step">
            <h3>Step 6: Getting Chat IDs</h3>
            <p>To send messages, you need chat IDs. Here are some ways to get them:</p>
            <ul>
                <li><strong>For users:</strong> Have them send a message to your bot, then use getUpdates</li>
                <li><strong>For groups:</strong> Add your bot to the group and use getUpdates</li>
                <li><strong>For channels:</strong> Use the channel username (e.g., @channelname)</li>
            </ul>
            <button type="button" onclick="getUpdates()">Get Recent Updates</button>
            <div id="updatesResult"></div>
        </div>
    </div>

    <script>
        function saveConfig() {
            const config = {
                bot_token: document.getElementById('botToken').value
            };
            
            fetch('/api/telegram/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/telegram/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function getBotInfo() {
            fetch('/api/telegram/bot-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('botInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>Bot Information:</strong><br>
                            Name: ${info.first_name}<br>
                            Username: @${info.username}<br>
                            ID: ${info.id}<br>
                            Can Join Groups: ${info.can_join_groups ? 'Yes' : 'No'}<br>
                            Can Read All Group Messages: ${info.can_read_all_group_messages ? 'Yes' : 'No'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get bot info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('botInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/telegram/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/telegram/unipile/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const accounts = data.accounts || [];
                    const telegramAccounts = accounts.filter(acc => acc.type === 'TELEGRAM');

                    if (telegramAccounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected Telegram Accounts:</strong><br>';
                        telegramAccounts.forEach(account => {
                            html += `📱 Account ID: ${account.id}<br>`;
                            html += `👤 Name: ${account.name || 'N/A'}<br>`;
                            html += `📞 Phone: ${account.phone || 'N/A'}<br>`;
                            html += `🔗 Status: ${account.status || 'Connected'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Connected</strong><br>
                                No Telegram accounts found. To add a Telegram account:<br>
                                1. Visit your <a href="https://dashboard.unipile.com" target="_blank">Unipile Dashboard</a><br>
                                2. Click "Add Account" → "Telegram"<br>
                                3. Follow the authentication process<br>
                                4. Return here and click "Check Connection Status"
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }



        function sendTestMessage() {
            const chatId = document.getElementById('testChatId').value;
            const message = document.getElementById('testMessage').value;
            
            if (!chatId || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both chat ID and message</div>';
                return;
            }
            
            fetch('/api/telegram/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chat_id: chatId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendBulkMessages() {
            const chatIdsText = document.getElementById('bulkChatIds').value;
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('bulkDelay').value);

            if (!chatIdsText || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter both chat IDs and message</div>';
                return;
            }

            // Parse chat IDs (one per line)
            const recipients = chatIdsText.split('\n')
                .map(chatId => chatId.trim())
                .filter(chatId => chatId.length > 0);

            if (recipients.length === 0) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter at least one chat ID</div>';
                return;
            }

            document.getElementById('bulkResult').innerHTML =
                '<div class="info">📤 Sending bulk messages... Please wait.</div>';

            fetch('/api/telegram/send-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results;
                    const successful = results.filter(r => r.result.success || r.result.ok).length;
                    const failed = results.length - successful;

                    let resultHtml = `<div class="success">✅ Bulk messaging completed!</div>`;
                    resultHtml += `<div class="info">📊 Results: ${successful} sent, ${failed} failed</div>`;

                    if (failed > 0) {
                        resultHtml += '<div class="error">❌ Failed messages:<ul>';
                        results.forEach(r => {
                            if (!r.result.success && !r.result.ok) {
                                resultHtml += `<li>${r.chat_id}: ${r.result.error || r.result.description}</li>`;
                            }
                        });
                        resultHtml += '</ul></div>';
                    }

                    resultDiv.innerHTML = resultHtml;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send bulk messages: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getUpdates() {
            fetch('/api/telegram/updates')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('updatesResult');
                if (data.success && data.data.result) {
                    const updates = data.data.result;
                    if (updates.length === 0) {
                        resultDiv.innerHTML = '<div class="info">No recent updates found.</div>';
                    } else {
                        let html = '<div class="success"><strong>Recent Updates:</strong><br>';
                        updates.slice(-5).forEach(update => {
                            if (update.message) {
                                const msg = update.message;
                                html += `Chat ID: ${msg.chat.id}, From: ${msg.from.first_name}, Text: "${msg.text || '[Media]'}"<br>`;
                            }
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get updates: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('updatesResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
