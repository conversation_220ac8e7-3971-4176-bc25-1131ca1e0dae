#!/usr/bin/env python3
"""
Test the enhanced API response for LinkedIn connection requests
"""

import sys
import os
import json
import requests
from datetime import datetime

def test_enhanced_api_response():
    """Test that the API returns enhanced error responses instead of generic 400 errors"""
    
    print("🔍 Testing Enhanced API Error Response")
    print("=" * 40)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # API endpoint
    api_url = "http://localhost:8000/api/linkedin/send-connection-message"
    
    # Test data that will likely cause a server error (based on recent logs)
    test_data = {
        "recipient_id": "demilade-adebanjo-554774202",
        "message": "hello"
    }
    
    print(f"\n📋 Test Parameters:")
    print(f"   API URL: {api_url}")
    print(f"   Recipient ID: {test_data['recipient_id']}")
    print(f"   Message: {test_data['message']}")
    
    try:
        print(f"\n🔍 Making API Request...")
        print("-" * 25)
        
        response = requests.post(api_url, json=test_data, timeout=30)
        
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        # Parse response
        try:
            response_data = response.json()
        except:
            response_data = {"raw_response": response.text}
        
        print(f"\n📊 Enhanced Response Analysis:")
        print("-" * 30)
        print(json.dumps(response_data, indent=2))
        
        # Analyze the response
        print(f"\n🔍 Response Analysis:")
        print("-" * 20)
        
        # Check if it's the enhanced error response
        if response.status_code in [503, 422, 401, 400] and isinstance(response_data, dict):
            # Check for enhanced error fields
            enhanced_fields = [
                "error_type", "reason", "solutions", "suggestion", 
                "retry_recommended", "fallback_available", "identifier_type"
            ]
            
            present_fields = [field for field in enhanced_fields if field in response_data]
            missing_fields = [field for field in enhanced_fields if field not in response_data]
            
            print(f"   Enhanced fields present: {len(present_fields)}/{len(enhanced_fields)}")
            print(f"   Present: {present_fields}")
            if missing_fields:
                print(f"   Missing: {missing_fields}")
            
            # Analyze specific fields
            if "error_type" in response_data:
                error_type = response_data["error_type"]
                print(f"   ✅ Error Type: {error_type}")
                
                if error_type == "server_error" and response.status_code == 503:
                    print(f"   ✅ Correct HTTP status for server error (503)")
                elif error_type == "validation_error" and response.status_code == 422:
                    print(f"   ✅ Correct HTTP status for validation error (422)")
                else:
                    print(f"   ⚠️  HTTP status {response.status_code} for error type {error_type}")
            
            if "solutions" in response_data:
                solutions = response_data["solutions"]
                print(f"   ✅ Solutions provided: {len(solutions)} options")
                for i, solution in enumerate(solutions[:3], 1):  # Show first 3
                    print(f"      {i}. {solution}")
                if len(solutions) > 3:
                    print(f"      ... and {len(solutions) - 3} more")
            
            if "retry_recommended" in response_data:
                retry = response_data["retry_recommended"]
                delay = response_data.get("retry_delay_seconds", "not specified")
                print(f"   ✅ Retry guidance: {retry} (delay: {delay}s)")
            
            if "fallback_available" in response_data:
                fallback = response_data["fallback_available"]
                print(f"   ✅ Fallback status: {fallback}")
            
            # Overall assessment
            if len(present_fields) >= 4:
                print(f"\n   ✅ Enhanced error response is working correctly!")
                print(f"   📋 Key improvements:")
                print(f"      • Specific error classification")
                print(f"      • Multiple solution options")
                print(f"      • Clear retry guidance")
                print(f"      • Proper HTTP status codes")
            else:
                print(f"\n   ⚠️  Enhanced error response partially working")
                print(f"   📝 Missing some enhanced fields")
        
        elif response.status_code == 200:
            print(f"   ✅ Request succeeded unexpectedly")
            print(f"   📝 This means the server error was resolved")
        
        else:
            print(f"   ❌ Generic error response (not enhanced)")
            print(f"   📝 Response: {response_data}")
        
        # Test different error scenarios
        print(f"\n🔍 Testing Different Error Scenarios")
        print("-" * 35)
        
        test_scenarios = [
            {
                "name": "Invalid identifier format",
                "data": {"recipient_id": "invalid@format", "message": "test"},
                "expected_status": 422,
                "expected_type": "validation_error"
            },
            {
                "name": "Empty recipient",
                "data": {"recipient_id": "", "message": "test"},
                "expected_status": 422,
                "expected_type": "validation_error"
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n   Testing: {scenario['name']}")
            print(f"   Data: {scenario['data']}")
            
            try:
                test_response = requests.post(api_url, json=scenario['data'], timeout=10)
                test_data = test_response.json() if test_response.headers.get('content-type', '').startswith('application/json') else {}
                
                print(f"   Status: {test_response.status_code} (expected: {scenario['expected_status']})")
                
                if "error_type" in test_data:
                    print(f"   Error Type: {test_data['error_type']} (expected: {scenario['expected_type']})")
                    
                    if (test_response.status_code == scenario['expected_status'] and 
                        test_data['error_type'] == scenario['expected_type']):
                        print(f"   ✅ Correct enhanced error response")
                    else:
                        print(f"   ⚠️  Unexpected response")
                else:
                    print(f"   ❌ No error_type field (not enhanced)")
                    
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
        
        print(f"\n" + "=" * 40)
        print("🎯 Test Summary:")
        
        if response.status_code in [503, 422] and "error_type" in response_data:
            print("✅ Enhanced API error response is working correctly!")
            print("✅ Proper HTTP status codes based on error type")
            print("✅ Comprehensive error information provided")
            print("✅ Clear guidance for resolution")
        elif response.status_code == 200:
            print("✅ API request succeeded (server error resolved)")
        else:
            print("❌ Enhanced error response needs improvement")
            print("📝 Check API endpoint implementation")
        
        print(f"\nTest completed at: {datetime.now().isoformat()}")
        return response_data
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
        print("💡 Make sure the API server is running: python integrations/api/api_endpoints.py")
        return None
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return None

def test_api_server_running():
    """Test if the API server is running"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"⚠️  API server responded with status {response.status_code}")
            return False
    except:
        print("❌ API server is not running")
        print("💡 Start the server with: python integrations/api/api_endpoints.py")
        return False

if __name__ == "__main__":
    print("🚀 Starting Enhanced API Response Test")
    print("=" * 45)
    
    # Check if API server is running
    if test_api_server_running():
        test_enhanced_api_response()
    else:
        print("\n📝 To start the API server:")
        print("   cd integrations/api")
        print("   python api_endpoints.py")
        print("   # or")
        print("   uvicorn api_endpoints:app --reload --port 8000")
