#!/usr/bin/env python3
"""
LinkedIn Configuration Validator
Validates and provides detailed feedback on LinkedIn configuration
"""

import json
import os
import sys
from datetime import datetime

def load_config():
    """Load and validate the configuration file"""
    # Try different possible locations for config.json
    possible_paths = [
        "config.json",
        "integrations/linkedin_integration/config.json",
        os.path.join(os.path.dirname(__file__), "config.json")
    ]

    config_path = None
    for path in possible_paths:
        if os.path.exists(path):
            config_path = path
            break

    if not config_path:
        return {"error": f"Configuration file not found in any of these locations: {possible_paths}"}
    
    if not os.path.exists(config_path):
        return {"error": f"Configuration file not found: {config_path}"}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return {"success": True, "config": config}
    except json.JSONDecodeError as e:
        return {"error": f"Invalid JSON in configuration file: {e}"}
    except Exception as e:
        return {"error": f"Error reading configuration file: {e}"}

def validate_unipile_config(config):
    """Validate Unipile configuration"""
    print("🔧 Validating Unipile Configuration")
    print("-" * 40)
    
    unipile_config = config.get("unipile", {})
    issues = []
    
    # Check API key
    api_key = unipile_config.get("api_key") or config.get("unipile_api_key")
    if not api_key:
        issues.append("❌ API key is missing")
        print("  API Key: ❌ Missing")
    elif api_key == "YOUR_UNIPILE_API_KEY":
        issues.append("❌ API key is still placeholder value")
        print("  API Key: ❌ Placeholder value")
    else:
        print("  API Key: ✅ Set")
    
    # Check account ID
    account_id = unipile_config.get("account_id", "")
    if not account_id:
        print("  Account ID: ⚠️  Empty (will auto-detect)")
    else:
        print(f"  Account ID: ✅ Set ({account_id})")
    
    # Check enabled status
    enabled = unipile_config.get("enabled", False)
    use_unipile = config.get("use_unipile", False)
    
    if not enabled:
        issues.append("⚠️  Unipile is disabled in config")
        print("  Enabled: ❌ No")
    else:
        print("  Enabled: ✅ Yes")
    
    if not use_unipile:
        issues.append("⚠️  use_unipile is set to false")
        print("  Use Unipile: ❌ No")
    else:
        print("  Use Unipile: ✅ Yes")
    
    # Check API URL
    api_url = unipile_config.get("api_url", "")
    expected_url = "https://api8.unipile.com:13814"
    if api_url != expected_url:
        issues.append(f"⚠️  API URL might be incorrect: {api_url}")
        print(f"  API URL: ⚠️  {api_url} (expected: {expected_url})")
    else:
        print("  API URL: ✅ Correct")
    
    return issues

def validate_linkedin_api_config(config):
    """Validate LinkedIn API configuration"""
    print("\n🔗 Validating LinkedIn API Configuration")
    print("-" * 45)
    
    linkedin_config = config.get("linkedin_api", {})
    issues = []
    
    required_fields = {
        "client_id": "Client ID",
        "client_secret": "Client Secret",
        "access_token": "Access Token", 
        "refresh_token": "Refresh Token",
        "person_id": "Person ID"
    }
    
    for field, label in required_fields.items():
        value = linkedin_config.get(field) or config.get(field)
        
        if not value:
            issues.append(f"❌ {label} is missing")
            print(f"  {label}: ❌ Missing")
        elif value.startswith("YOUR_LINKEDIN_"):
            issues.append(f"❌ {label} is still placeholder value")
            print(f"  {label}: ❌ Placeholder")
        else:
            # Mask sensitive values for display
            if field in ["client_secret", "access_token", "refresh_token"]:
                display_value = value[:8] + "..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"  {label}: ✅ Set ({display_value})")
    
    # Check API version and base URL
    api_version = config.get("api_version", "")
    base_url = config.get("base_url", "")
    
    if api_version != "v2":
        issues.append(f"⚠️  API version should be 'v2', found: {api_version}")
        print(f"  API Version: ⚠️  {api_version} (should be 'v2')")
    else:
        print("  API Version: ✅ v2")
    
    if base_url != "https://api.linkedin.com":
        issues.append(f"⚠️  Base URL should be 'https://api.linkedin.com', found: {base_url}")
        print(f"  Base URL: ⚠️  {base_url}")
    else:
        print("  Base URL: ✅ Correct")
    
    return issues

def validate_messaging_settings(config):
    """Validate messaging settings and limits"""
    print("\n📨 Validating Messaging Settings")
    print("-" * 35)
    
    issues = []
    
    # Check InMail settings
    inmail_settings = config.get("inmail_settings", {})
    max_subject = inmail_settings.get("max_subject_length", 200)
    max_message = inmail_settings.get("max_message_length", 1900)
    
    if max_subject > 200:
        issues.append("⚠️  InMail subject length limit too high (LinkedIn limit: 200)")
        print(f"  Subject Length Limit: ⚠️  {max_subject} (LinkedIn limit: 200)")
    else:
        print(f"  Subject Length Limit: ✅ {max_subject}")
    
    if max_message > 1900:
        issues.append("⚠️  InMail message length limit too high (LinkedIn limit: ~1900)")
        print(f"  Message Length Limit: ⚠️  {max_message} (LinkedIn limit: ~1900)")
    else:
        print(f"  Message Length Limit: ✅ {max_message}")
    
    # Check messaging limits
    messaging_limits = config.get("messaging_limits", {})
    inmails_per_day = messaging_limits.get("inmails_per_day", 20)
    connections_per_day = messaging_limits.get("connection_requests_per_day", 100)
    
    if inmails_per_day > 50:
        issues.append("⚠️  Daily InMail limit very high - may trigger LinkedIn restrictions")
        print(f"  Daily InMail Limit: ⚠️  {inmails_per_day} (recommended: ≤20)")
    else:
        print(f"  Daily InMail Limit: ✅ {inmails_per_day}")
    
    if connections_per_day > 200:
        issues.append("⚠️  Daily connection request limit very high - may trigger restrictions")
        print(f"  Daily Connection Limit: ⚠️  {connections_per_day} (recommended: ≤100)")
    else:
        print(f"  Daily Connection Limit: ✅ {connections_per_day}")
    
    return issues

def test_api_connectivity(config):
    """Test actual API connectivity"""
    print("\n🧪 Testing API Connectivity")
    print("-" * 30)
    
    try:
        # Import the LinkedIn messaging module
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        linkedin = LinkedInMessaging(use_unipile=True)
        
        # Test connection status
        print("  Testing connection status...")
        status = linkedin.get_connection_status()
        
        # Unipile connectivity
        unipile_status = status.get('unipile', {})
        unipile_available = unipile_status.get('available', False)
        unipile_connected = unipile_status.get('connected', False)
        
        print(f"  Unipile Available: {'✅' if unipile_available else '❌'}")
        print(f"  Unipile Connected: {'✅' if unipile_connected else '❌'}")
        
        if unipile_connected:
            accounts = unipile_status.get('accounts', [])
            print(f"  LinkedIn Accounts: {len(accounts)}")
            for acc in accounts:
                print(f"    - {acc.get('id')} ({acc.get('type')})")
        
        # LinkedIn API connectivity
        linkedin_status = status.get('linkedin_api', {})
        linkedin_available = linkedin_status.get('available', False)
        linkedin_connected = linkedin_status.get('connected', False)
        
        print(f"  LinkedIn API Available: {'✅' if linkedin_available else '❌'}")
        print(f"  LinkedIn API Connected: {'✅' if linkedin_connected else '❌'}")
        
        # Overall assessment
        if unipile_connected:
            print("  🎉 Primary method (Unipile) is working!")
            return []
        elif linkedin_connected:
            print("  ⚠️  Only fallback method (LinkedIn API) is working")
            return ["⚠️  Primary method (Unipile) not available"]
        else:
            print("  ❌ No working connection found")
            return ["❌ No API connectivity"]
            
    except Exception as e:
        print(f"  ❌ Connectivity test failed: {e}")
        return [f"❌ Connectivity test error: {str(e)}"]

def generate_config_report(config, all_issues):
    """Generate a comprehensive configuration report"""
    print("\n📋 Configuration Report")
    print("=" * 50)
    
    total_issues = len(all_issues)
    critical_issues = len([i for i in all_issues if i.startswith("❌")])
    warnings = len([i for i in all_issues if i.startswith("⚠️")])
    
    print(f"Total Issues Found: {total_issues}")
    print(f"  Critical Issues: {critical_issues}")
    print(f"  Warnings: {warnings}")
    
    if total_issues == 0:
        print("\n🎉 Configuration is perfect! No issues found.")
        print("✅ Your LinkedIn messaging integration is ready to use!")
    else:
        print(f"\n📝 Issues to Address:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        if critical_issues == 0:
            print("\n✅ No critical issues found. Configuration should work with warnings addressed.")
        else:
            print(f"\n❌ {critical_issues} critical issues must be fixed before the integration will work.")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if critical_issues > 0:
        print("  1. Run setup_config.py to configure missing credentials")
        print("  2. Focus on Unipile setup first (easier and more reliable)")
        print("  3. Test configuration after making changes")
    else:
        print("  1. Test messaging functionality with test_messaging.py")
        print("  2. Try sending a test message to verify everything works")
        print("  3. Monitor success rates and adjust limits if needed")

def main():
    """Main validation function"""
    print("LinkedIn Configuration Validator")
    print("=" * 50)
    print(f"Validation started at: {datetime.now().isoformat()}")
    
    # Load configuration
    result = load_config()
    if "error" in result:
        print(f"❌ {result['error']}")
        return
    
    config = result["config"]
    print("✅ Configuration file loaded successfully")
    
    # Run all validations
    all_issues = []
    
    all_issues.extend(validate_unipile_config(config))
    all_issues.extend(validate_linkedin_api_config(config))
    all_issues.extend(validate_messaging_settings(config))
    all_issues.extend(test_api_connectivity(config))
    
    # Generate final report
    generate_config_report(config, all_issues)
    
    print(f"\nValidation completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
